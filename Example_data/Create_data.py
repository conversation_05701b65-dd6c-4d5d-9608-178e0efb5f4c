import csv

# Read the input file
input_file = './Example_data/SCC_nullifyDispersion_D2MHz-28_KMHz66N32_epsShift7_epsRelSide0.7_D2Extra-0.02.txt'
output_data = []

# with open(input_file, 'r') as f:
#     # Read header
#     header = f.readline().strip()
#     output_data.append(header)
    
#     # Read CSV data
#     reader = csv.reader(f)
#     for row in reader:
#         if row:  # Skip empty rows
#             # Convert row values to appropriate types
#             mu = float(row[0])
#             aphcnorm = float(row[1])
#             phase = float(row[2])
            
#             # Add original row
#             output_data.append(f"{mu},{aphcnorm},{phase}")
            
#             # Add new row with negated APhCNorm
#             output_data.append(f"{mu},{-aphcnorm},{phase}")

# # Write to output file (overwriting original)
# with open(input_file, 'w') as f:
#     f.write('\n'.join(output_data))

import numpy as np
pos_rows = []
neg_rows = []
with open(input_file, 'r') as f:
    next(f)

    reader = csv.reader(f)
    for row in reader:
        if row:
            aphcnorm = float(row[1])
            row_data = [float(x) for x in row]
            if aphcnorm > 0:
                pos_rows.append(row_data)
            else:
                neg_rows.append(row_data)

print(np.array(pos_rows))
print(np.array(neg_rows))

                
