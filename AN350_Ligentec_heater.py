import gdsfactory as gf
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from gdsfactory.typings import LayerSpec 

def straight(length=10, width: float = 1.9, layer=(1, 0)):
    """Creates a straight waveguide for IO."""
    c = gf.Component()
    c.add_polygon([(0, 0), (length, 0), (length, width), (0, width)], layer=layer)
    c.add_port(
        name="o1", center=(0, width / 2), width=width, orientation=180, layer=layer
    )
    c.add_port(
        name="o2", center=(length, width / 2), width=width, orientation=0, layer=layer
    )
    return c

@gf.cell
def AN350BB_EdgeCoupler_Lensed_C(
        
) -> gf.Component:
    """Lensed edge coupler from Ligentec."""

    c = gf.Component()

    box = straight(length=430, width=30, layer=(13, 2))
    r1 = c << box
    text = c << gf.components.text(text="AN350BB_EdgeCoupler_Lensed_C"
                                   , size = 5, position = (160, 12), layer=(15, 2))
    input = straight(length=5, width=1.9, layer=(2, 2))
    r2 = c << input
    r2.movey(15 - 1/2)

    input_2 = straight(length=6, width=1.9, layer=(2, 0))

    r2_2 = c << input_2
    r2_2.move((5-6,15 - 1/2))
    
    output = straight(length=10, width=30, layer=(101, 2))
    r3 = c << output 
    r3.movex(430 - 10)
    c.add_port("o1", port=r2_2.ports["o1"], layer=(2, 0))
    c.add_port("o2", port=r3.ports["o2"])  
    c.flatten()
    return c

@ gf.cell
def ring_with_heater(
    radius: float = 79.49,
    ring_width: float = 2.2,
    wg_width: float = 1.9,
    lam: float = 0.4655,
    corrugation_amplitude: float = 0.2,
    layer: LayerSpec = (2, 0),
    n_points: int = 12000,
    gap: int = 0.5,
    dy: int = -240,
    dx: int = 30,
    i: int = 0,
    num_rings: int = 14
) -> gf.Component:
    """Creates a ring resonator with an adjacent bus waveguide."""
    c = gf.Component(f"ring_with_bus_r{radius}_w{ring_width}_amp{corrugation_amplitude}_gap{gap}_i{i}")
    
    # Calculate nominal outer and mean inner radii
    R_outer = radius + ring_width / 2
    R_inner_mean = radius - ring_width / 2

    corrugation_periods = round(2*np.pi*R_inner_mean/lam)
    
    if n_points < 3*corrugation_periods and corrugation_periods > 0:
        print(f"Warning: n_points ({n_points}) might be too low for {corrugation_periods} periods. "
              f"Consider increasing to at least {3*corrugation_periods} for smoother features.")
    if n_points < 3:
        raise ValueError("n_points must be at least 3.")
    
    # Angular points for defining the polygon
    theta = np.linspace(0, 2 * np.pi, n_points, endpoint=True)

    # Outer boundary points (circular)
    x_outer = R_outer * np.cos(theta)
    y_outer = R_outer * np.sin(theta)
    outer_points = list(zip(x_outer, y_outer))

    r_inner_modulated = R_inner_mean - corrugation_amplitude * np.sin(corrugation_periods * theta)
    r_inner_modulated = np.maximum(0, r_inner_modulated)
    x_inner = r_inner_modulated * np.cos(theta)
    y_inner = r_inner_modulated * np.sin(theta)
    inner_points = list(zip(x_inner, y_inner))

    all_polygon_points = outer_points + inner_points[::-1]
    c.add_polygon(all_polygon_points, layer=layer)
  
    # Add bends
    angle = 320
    bend_cir = c << gf.components.bend_circular(angle = angle, radius = radius, width = ring_width, layer = (30, 0))
    bend_cir.movey(-radius )
    rotate_angle = -(90 - (360 - angle)/2)
    bend_cir.rotate(rotate_angle)
    bend_1 = c << gf.components.bend_euler(radius=radius/6, angle = 70, width = ring_width, layer = (30, 0)) # 90 - (360 - 320)/2 = 70 to ensure the ports are horizontal

    bend_1.mirror_x()
    bend_1.connect("o1", bend_cir.ports["o2"])
    bend_2 = c << gf.components.bend_euler(radius=radius/6, angle = 70, width = ring_width, layer = (30, 0))
    bend_2.connect("o1", bend_cir.ports["o1"])

    # Add heater
    size_pad = 0.36
    num_pads = 15
    pads = gf.components.pad(size=(size_pad, size_pad), layer = (31, 0))
    heater = c.add_ref(pads, columns=num_pads, rows=num_pads, column_pitch=0.75, row_pitch=0.75)
    offset = 0.5
    bbox = gf.components.bbox(heater, top=offset, bottom=offset, left=offset, right=offset, layer = (30, 0)).dup()
    bbox_ref = c << bbox
    b = gf.get_component(bbox)
    co = b.dbbox()
    xmin, ymin, xmax, ymax = co.left, co.bottom, co.right, co.top
    bbox.add_port(name="o1", center=(xmax, (ymax + ymin)/2), width=bbox.size_info.width, orientation=0, layer = (30, 0))
    bbox.add_port(name="o2", center=(xmin, (ymax + ymin)/2), width=bbox.size_info.width, orientation=180, layer = (30, 0))
    bbox_2 = bbox.dup()
    bbox_2_ref = c << bbox_2

    taper = gf.components.taper(width1=bbox.size_info.width, width2=ring_width, length=10, layer = (30, 0))
    taper_ref = c << taper
    taper_ref.connect("o2", bend_1.ports["o2"])
    taper_ref_2 = c << taper.dup()
    taper_ref_2.connect("o2", bend_2.ports["o2"])
    bbox_ref.connect("o1", taper_ref_2.ports["o1"])
    heater.move([bbox_ref.xmin + offset + size_pad/2, bbox_ref.ymin + offset + size_pad/2])
    bbox_2_ref.connect("o1", taper_ref.ports["o1"])
    heater_2 = c.add_ref(pads, columns=num_pads, rows=num_pads, column_pitch=0.75, row_pitch=0.75)
    heater_2.move([bbox_2_ref.xmin + offset + size_pad/2, bbox_2_ref.ymin + offset + size_pad/2])

    # Add metal bend 1
    if i % 2 != 0:
        offset_mb = 300
    else:
        offset_mb = 150

    x_start, y_start = bbox_2_ref.xmax + 5, (bbox_2_ref.ymin + bbox_2_ref.ymax)/2

    if i % num_rings == 0:
        x1, y1 = x_start - offset_mb, y_start
        x2, y2 = x1 - 15, y1 - 15
        x3, y3 = x2, y2 - 100
        points = [(x_start, y_start), (x1, y1), (x2, y2), (x3, y3)]
    else:
        x3, y3 = x_start - offset_mb, y_start
        points = [(x_start, y_start), (x3, y3)]
   
    mb = gf.Path(points)
    mb_component = gf.path.extrude(mb, layer=(32, 0), width=20)
    mb_ref = c << mb_component

    mb_pad_1 = gf.components.pad(size=(110, 110), layer = (32, 0))
    mb_pad_1_ref = c << mb_pad_1
    mb_pad_1_ref.move([x3, y3])
    mb_pad_2 = gf.components.pad(size=(90, 90), layer = (33, 0))
    mb_pad_2_ref = c << mb_pad_2
    mb_pad_2_ref.move([x3, y3])

    # Add metal bend 2
    x_start_2, y_start_2 = (bbox_ref.xmin + bbox_ref.xmax)/2, bbox_ref.ymax + 5
    if i % num_rings == 0:
        offset_mb_2 = 200
    else:
        offset_mb_2 = 100

    x1_2, y1_2 = x_start_2, y_start_2 - offset_mb_2
    x2_2, y2_2 = x1_2 - 15, y1_2 - 15
    x3_2, y3_2 = x3, y2_2
    
    points = [(x_start_2, y_start_2), (x1_2, y1_2), (x2_2, y2_2), (x3_2, y3_2)]
    mb_2 = gf.Path(points)
    mb_component_2 = gf.path.extrude(mb_2, layer=(32, 0), width=20)
    mb_ref_2 = c << mb_component_2
    mb_pad_1_2 = gf.components.pad(size=(110, 110), layer = (32, 0))
    mb_pad_1_2_ref = c << mb_pad_1_2
    mb_pad_1_2_ref.move([x3_2, y3_2])
    mb_pad_2_2 = gf.components.pad(size=(90, 90), layer = (33, 0))
    mb_pad_2_2_ref = c << mb_pad_2_2
    mb_pad_2_2_ref.move([x3_2, y3_2])

    # Add bus waveguide
    P = gf.Path()
    straight = gf.path.straight(length=radius/1.5)
    left_turn = gf.path.euler(radius=radius/1.2, angle=90)
    right_turn = gf.path.euler(radius=radius/1.2, angle=-90)

    P.append(straight)
    
    if (i + 1) % num_rings and i % (num_rings):
        P.append(left_turn)
        P.append(right_turn)
        P.mirror_y()
    
    P.rotate(90)
    P.movey(-35)

    if i % num_rings == 0:
        P.movex(radius + ring_width/2 + gap + wg_width/2)
    else: 
        P.movex(-radius - ring_width/2 - gap - wg_width/2)

    d = gf.path.extrude(P, layer=(2, 0), width=wg_width)
    wg_bus = c << d
    c.add_port("o1", port=wg_bus.ports["o1"])
    c.add_port("o2", port=wg_bus.ports["o2"])

    c.flatten()
    return c

@gf.cell
def create_array_of_rings(
        num_rings: int = 12,
        num_arrays: int = 1,
        dx: float = -30,
        dy: float = 220,
        params_for_cell_array: list[dict] = None
) -> gf.Component:
    """Creates an array of ring resonators with heaters connected to IO ports."""

    c = gf.Component()
    
    j, k, delta_x = 0, 0, 0
    port_1, port_2 = [], []
    Top_IO_ports = []
    Bot_IO_ports = []
    for i in range(len(params_for_cell_array)):

        # Create ring arrays
        current_params = params_for_cell_array[i]
        ring = ring_with_heater(dy = dy, i = i, num_rings = num_rings, **current_params)
        ring_ref = c << ring

        if i % num_rings == 0:
            j = 0
            if i > 0:
                k+=7*dx
            ring_ref.move((k + 30, j*dy +50 ))
            
        else:
            ring_ref.move((k, j*dy))
        j+=1
        k+=dx

        # Connect taper to bus waveguide
        if i% num_rings == 0:
            taper_1 = gf.components.taper(
                length=num_rings*dy - 180,
                width1=params_for_cell_array[i]["wg_width"],
                width2=1.9,
                layer=(2, 0),
            )

            taper_2 = gf.components.taper(
                length=100,
                width1=params_for_cell_array[i]["wg_width"],
                width2=1.9,
                layer=(2, 0),
            )

        elif (i + 1) % num_rings == 0:
            taper_1 = gf.components.taper(
                length=80,
                width1=params_for_cell_array[i]["wg_width"],
                width2=1.9,
                layer=(2, 0),
            )

            taper_2 = gf.components.taper(
                length=num_rings*dy - 180,
                width1=params_for_cell_array[i]["wg_width"],
                width2=1.9,
                layer=(2, 0),
            )
        else:
            taper_1 = gf.components.taper(
                length=abs((num_rings - j)*dy) - 100,
                width1=params_for_cell_array[i]["wg_width"],
                width2=1.9,
                layer=(2, 0),
            )

            taper_2 = gf.components.taper(
                length=abs(j*dy) - 180,
                width1=params_for_cell_array[i]["wg_width"],
                width2=1.9,
                layer=(2, 0),
            )
        t_1 = c << taper_1
        t_1.connect("o1", ring_ref.ports["o2"])
        t_2 = c << taper_2
        t_2.connect("o1", ring_ref.ports["o1"])

        port_1.append(t_1.ports["o2"])
        port_2.append(t_2.ports["o2"])

        
        off_set_IO = 41.5
        if i % 10 == 0:
            delta_x += -35
            text = gf.components.text(text= f"{i + 1}", size = 28).copy().rotate(90)
            text_ref = c << text
            text_ref.move(( -i*off_set_IO + delta_x + 135,abs(num_rings*dy) + 300))
            text_bot = text.dup()
            text_bot_ref = c << text_bot
            text_bot_ref.move(( -i*off_set_IO + delta_x + 135,dy - 730))

        IO_top = AN350BB_EdgeCoupler_Lensed_C().dup().rotate(90)
        IO_top.move(( -i*off_set_IO + delta_x + 100,abs(num_rings*dy) + 100))
        Top_IO_ports.append(IO_top.ports["o1"]) # or IO_top.ports[0]

        IO_bot = AN350BB_EdgeCoupler_Lensed_C().dup().rotate(-90)
        IO_bot = IO_bot.move(( -i*off_set_IO + delta_x + 100 - 30,dy - 500))
        Bot_IO_ports.append(IO_bot.ports["o1"]) # or IO_bot.ports[0]

        c << IO_top
        c << IO_bot

    # Connect top IO ports to bus waveguides
    custom_cross_section = gf.cross_section.cross_section(width=1.9, layer=(2, 0))  # Example layer (1, 0)
    routes_1 = gf.routing.route_bundle_sbend(
        c,
        Top_IO_ports, 
        port_1,
        allow_layer_mismatch=True,
        # allow_width_mismatch=True,
        enforce_port_ordering=False,
        cross_section = custom_cross_section,
    )

    routes_2 = gf.routing.route_bundle_sbend(
        c,
        Bot_IO_ports, 
        port_2,
        allow_layer_mismatch=True,
        # allow_width_mismatch=True,
        enforce_port_ordering=False,
        cross_section = custom_cross_section,
    )

    c.flatten()

    return c

def load_params_from_excel(filename: str = 'LGT-MPW-AN350-01_MEngine01_withSimValues.xlsx') -> list[dict]:
    """Loads ring parameters from an Excel file."""
    try:
        df = pd.read_excel(filename)
        array = df.to_numpy()
        r, _ = array.shape
        ring_params = []
        for i in range(6, r):
            params = {
                "radius": array[i, 3],
                "ring_width": array[i, 4],
                "corrugation_amplitude": array[i, 6],
                "wg_width": array[i, 9],
                "gap": array[i, 11],
            }
            ring_params.append(params)
        return ring_params
    except FileNotFoundError:
        print(f"Error: File '{filename}' not found.")
        return []
    except Exception as e:
        print(f"Error reading Excel file: {e}")
        return []

if __name__ == "__main__":
    # gf.clear_cache()

    loaded_ring_params = load_params_from_excel()

    num_rings = 14
    num_arrays = len(loaded_ring_params) // num_rings + 1

    r_arr = create_array_of_rings(num_rings = num_rings, num_arrays = num_arrays, params_for_cell_array = loaded_ring_params)    
    # r = AN350BB_EdgeCoupler_Lensed_C()
    # r_arr = ring_with_heater()
    r_arr.show()

    # write gds file
    # r_arr.write_gds("AN350_Ligentec_heater.gds")




