import gdsfactory as gf
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from gdsfactory.config import PATH
from gdsfactory.technology import lyp_to_dataclass
import warnings
import csv

from gdsfactory.typings import Layer
from gdsfactory.technology.layer_map import LayerMap
from gdsfactory.technology import LayerViews
layer_views = LayerViews(filepath="LIGENTEC_AN350.lyp")
from testSpiral import custom_spiral

OVERSAMPLING_FACTOR_PHC = 12

from LIGENTEC_AN350 import *

# pdk = gf.Pdk(
#     name="LIGENTEC_AN350",
#     layers=layer_views.layers,
#     layer_views=layer_views,
# )
# pdk.activate()


from gdsfactory.typings import LayerSpec


lyp_path = "LIGENTEC_AN350.lyp"
tech = lyp_to_dataclass(lyp_path)
print(tech)

@gf.cell
def straight(length=10, width: float = 1.0, layer=(1, 0)):
    """Creates a straight waveguide for IO."""
    c = gf.Component()
    c.add_polygon([(0, 0), (length, 0), (length, width), (0, width)], layer=layer)
    c.add_port(
        name="o1", center=(0, width / 2), width=width, orientation=180, layer=layer
    )
    c.add_port(
        name="o2", center=(length, width / 2), width=width, orientation=0, layer=layer
    )
    return c

@gf.cell
def AN350BB_EdgeCoupler_Lensed_C(
        
) -> gf.Component:
    """Lensed edge coupler from Ligentec."""

    c = gf.Component()

    box = straight(length=430, width=30, layer=(13, 2))
    r1 = c << box
    text = c << gf.components.text(text="AN350BB_EdgeCoupler_Lensed_C"
                                   , size = 5, position = (160, 12), layer=(2, 0))
    input = straight(length=5, width=1.0, layer=(2, 2))
    r2 = c << input
    r2.movey(15 - 1/2)

    # input_2 = straight(length=6, width=1.9, layer=(2, 0))

    # r2_2 = c << input_2
    # r2_2.move((5-6,15 - 1/2))
    
    output = straight(length=10, width=30, layer=(101, 2))
    r3 = c << output 
    r3.movex(430 - 10)

    c.move((-5, -30/2))

    c.add_port("o1", port=r2.ports["o2"], layer=(2, 0))
    c.add_port("o2", port=r3.ports["o2"])  
    c.flatten()
    return c

@gf.cell
def ring_with_heater(
    radius: float = 79.49,
    ring_width: float = 2.2,
    wg_width: float = 1.9,
    lam: float = 0.4655,
    corrugation_amplitude: float = 0.1,
    m: int = 1080,
    layer: LayerSpec = (2, 0),
    n_points: int = 50000,
    gap: int = 0.5,
    dy: int = -240,
    dx: int = 30,
    i: int = 0,
    num_rings: int = 14,
    superimpose: str = "None", 
    heater: bool = False
) -> gf.Component:
    
    """Creates a ring resonator with an adjacent bus waveguide."""
    c = gf.Component(f"ring_with_bus_r{radius}_w{ring_width}_amp{corrugation_amplitude}_gap{gap}_i{i}")
    
    if pd.isna(superimpose):
        # Calculate the radius from the number of periods 2*pi*R = m*lam
        radius = m*lam/(2*np.pi)
        # Calculate nominal outer and mean inner radii
        R_outer = radius + ring_width / 2
        R_inner_mean = radius - ring_width / 2

        # corrugation_periods = round(2*np.pi*R_inner_mean/lam)
        corrugation_periods = int(m)

        n_points = OVERSAMPLING_FACTOR_PHC*corrugation_periods

        print(radius)
        if n_points < 3*corrugation_periods and corrugation_periods > 0:
            print(f"Warning: n_points ({n_points}) might be too low for {corrugation_periods} periods. "
                f"Consider increasing to at least {3*corrugation_periods} for smoother features.")
        if n_points < 3:
            raise ValueError("n_points must be at least 3.")
        
        # Angular points for defining the polygon
        theta = np.linspace(0, 2 * np.pi, n_points, endpoint=True)

        R_inner_modulated = R_inner_mean - corrugation_amplitude * np.sin(corrugation_periods * theta)
        R_inner_modulated = np.maximum(0, R_inner_modulated)

        x_inner = R_inner_modulated * np.cos(theta)
        y_inner = R_inner_modulated * np.sin(theta)
        inner_points = list(zip(x_inner, y_inner))

        all_polygon_points = outer_points + inner_points[::-1]
    
    else:
        base_mode_order = int(np.round(m))
        if base_mode_order %2 != 0:
            warnings.warn(f'Base mode order `m` is ODD (={base_mode_order})')

        PHC_root = './Example_data/'
        PHC_file = superimpose + '.txt'

        # df = pd.read_csv(PHC_root + PHC_file,
        #                  converters={'APhCNorm': lambda s: complex(s.replace('i', 'j'))})
        # relative_mode_orders = df['mu'].to_numpy()
        # complex_amplitudes_norm = df['APhCNorm'].to_numpy()
        # angular_phase_offsets = df['phase'].to_numpy()

        # Read corrupgation profile
        pos_rows = []
        neg_rows = []
        with open(PHC_root + PHC_file, 'r') as f:
            next(f)
            reader = csv.reader(f)
            for row in reader:
                if row:
                    aphcnorm = float(row[1])
                    row_data = [float(x) for x in row]
                    if aphcnorm > 0:
                        pos_rows.append(row_data)
                    else:
                        neg_rows.append(row_data)
        pos_rows = np.array(pos_rows)
        neg_rows = np.array(neg_rows)

        modulation_mode_orders = 2*pos_rows[:, 0] + base_mode_order

        num_points = OVERSAMPLING_FACTOR_PHC * int(np.max(modulation_mode_orders)) + 1
        phi = np.linspace(-np.pi, np.pi, num_points, endpoint=True)

        complex_amplitudes_inner = abs(corrugation_amplitude * pos_rows[:, 1])
        complex_amplitudes_outer = abs(corrugation_amplitude * neg_rows[:, 1])
        PhC_profile_inner = np.real(0.5 * np.sum(complex_amplitudes_inner * np.cos(np.outer(modulation_mode_orders, phi).T + modulation_mode_orders*angular_phase_offsets), axis=1))
        PhC_profile_outer = np.real(0.5 * np.sum(complex_amplitudes_outer * np.cos(np.outer(modulation_mode_orders, phi).T + modulation_mode_orders*angular_phase_offsets), axis=1))

        radius_inner_mean = radius - ring_width / 2
        radius_outer = radius + ring_width / 2
        
        R_outer_modulated = radius_outer - PhC_profile_outer
        R_outer_modulated = np.maximum(0, R_outer_modulated)

        R_inner_modulated = radius_inner_mean - PhC_profile_inner
        R_inner_modulated = np.maximum(0, R_inner_modulated)

        # inner points
        x_inner = R_inner_modulated * np.cos(phi)
        y_inner = R_inner_modulated * np.sin(phi)
        inner_points = list(zip(x_inner, y_inner))

        # outer points
        x_outer = R_outer_modulated * np.cos(phi)
        y_outer = R_outer_modulated * np.sin(phi)
        outer_points = list(zip(x_outer, y_outer))

        all_polygon_points = np.concatenate((outer_points, inner_points[::-1]))
        
    c.add_polygon(all_polygon_points, layer=layer)
    
    if heater:
        # Add heater bends
        angle = 320
        if radius < 79.48:
            ring_width = ring_width*1.4
        else:
            ring_width = ring_width*1.1
        bend_cir = c << gf.components.bend_circular(angle = angle, radius = radius, width = ring_width, layer = (30, 0))
        bend_cir.movey(-radius )
        rotate_angle = -(90 - (360 - angle)/2)
        bend_cir.rotate(rotate_angle)
        bend_1 = c << gf.components.bend_euler(radius=radius/6, angle = 70, width = ring_width, layer = (30, 0)) # 90 - (360 - 320)/2 = 70 to ensure the ports are horizontal

        bend_1.mirror_x()
        bend_1.connect("o1", bend_cir.ports["o2"])
        bend_2 = c << gf.components.bend_euler(radius=radius/6, angle = 70, width = ring_width, layer = (30, 0))
        bend_2.connect("o1", bend_cir.ports["o1"])

        # Add heater
        size_pad = 0.36
        num_pads = 10
        pads = gf.components.pad(size=(size_pad, size_pad), layer = (31, 0))
        heater_array = c.add_ref(pads, columns=num_pads, rows=num_pads, column_pitch=0.71, row_pitch=0.71)
        offset = 1
        bbox = gf.components.bbox(heater_array, top=offset, bottom=offset, left=offset, right=offset, layer = (30, 0)).dup()
        bbox_ref = c << bbox
        b = gf.get_component(bbox)
        co = b.dbbox()
        xmin, ymin, xmax, ymax = co.left, co.bottom, co.right, co.top
        bbox.add_port(name="o1", center=(xmax, (ymax + ymin)/2), width=bbox.size_info.width, orientation=0, layer = (30, 0))
        bbox.add_port(name="o2", center=(xmin, (ymax + ymin)/2), width=bbox.size_info.width, orientation=180, layer = (30, 0))
        bbox_2 = bbox.dup()
        bbox_2_ref = c << bbox_2

        taper = gf.components.taper(width1=bbox.size_info.width, width2=ring_width, length=10, layer = (30, 0))
        taper_ref = c << taper
        taper_ref.connect("o2", bend_1.ports["o2"])
        taper_ref_2 = c << taper.dup()
        taper_ref_2.connect("o2", bend_2.ports["o2"])
        bbox_ref.connect("o1", taper_ref_2.ports["o1"])
        heater_array.move([bbox_ref.xmin + offset + size_pad/2, bbox_ref.ymin + offset + size_pad/2])
        bbox_2_ref.connect("o1", taper_ref.ports["o1"])
        heater_2 = c.add_ref(pads, columns=num_pads, rows=num_pads, column_pitch=0.71, row_pitch=0.71)
        heater_2.move([bbox_2_ref.xmin + offset + size_pad/2, bbox_2_ref.ymin + offset + size_pad/2])

        # Route to metal IO
        pt = c << gf.components.pad(size=(15, 15), layer = (32, 0), port_orientation=180)
        pt.move([bbox_2_ref.center[0], bbox_2_ref.center[1]])
        pt_2 = c << gf.components.pad(size=(15, 15), layer = (32, 0), port_orientation=180)
        pt_2.move([bbox_ref.center[0], bbox_ref.center[1]])

        # Add electrical ports to c
        c.add_port("e1", port=pt.ports["e1"], layer=(32, 0))
        c.add_port("e2", port=pt_2.ports["e1"], layer=(32, 0))

        mb_pad_1 = c << gf.components.pad(size=(110, 110), layer = (32, 0), port_orientations=0, )
        mb_pad_1_p = c << gf.components.pad(size=(90, 90), layer = (33, 0), port_orientations=0)

        off_set_pad = -300 + dx*(num_rings - 1 - (i % num_rings))
        mb_pad_1.move([off_set_pad, 80])
        mb_pad_1_p.move([off_set_pad, 80])

        cross_section = gf.cross_section.cross_section(width=pt.size_info.width, layer=(32, 0))
        route = gf.routing.route_single_electrical(
        c,
        pt.ports["e1"],
        mb_pad_1.ports["pad"],
        start_straight_length=20,
        cross_section=cross_section,)

        mb_pad_2 = c << gf.components.pad(size=(110, 110), layer = (32, 0), port_orientations=0)
        mb_pad_2_p = c << gf.components.pad(size=(90, 90), layer = (33, 0), port_orientations=0)
        mb_pad_2.move([off_set_pad, -80])
        mb_pad_2_p.move([off_set_pad, -80])
        route_2 = gf.routing.route_single_electrical(
        c,
        pt_2.ports["e1"],
        mb_pad_2.ports["pad"],
        start_straight_length=20,
        cross_section=cross_section,)

        # Add text for ring number
        text = gf.components.text(text= f"{i + 1}", size = 15, layer = (2, 0))
        text_ref = c << text
        text_ref.rotate(-90)
        text_ref.move((off_set_pad-text.size_info.height/2, text.size_info.width/2))
        

    # Add bus waveguide
    xs = gf.cross_section.cross_section(width=wg_width, layer=(2, 0))
    wg_bus = gf.components.straight(length=radius/1.5, cross_section=xs)
    wg_bus_ref = c << wg_bus
    wg_bus_ref.rotate(90)
    wg_bus_ref.movey(-35)
    if i % num_rings == 0:
        wg_bus_ref.movex(radius + ring_width/2 + gap + wg_width/2)
    else: 
        wg_bus_ref.movex(-radius - ring_width/2 - gap - wg_width/2)
    
    if i % num_rings != 0 and (i + 1) % num_rings != 0 :
        bend_1 = gf.components.bend_euler(radius=int(radius/0.9), width = wg_width, layer = (2, 0))
        bend_1_ref = c << bend_1
        bend_1_ref.mirror_x()
        bend_1_ref.connect("o1", wg_bus_ref.ports["o2"])
        
        bend_2 = gf.components.bend_euler(radius=int(radius/0.9), width = wg_width, layer = (2, 0))
        bend_2_ref = c << bend_2
        bend_2_ref.connect("o1", bend_1_ref.ports["o2"])
        c.add_port("o2", port=bend_2_ref.ports["o2"])
    else:
        c.add_port("o2", port=wg_bus_ref.ports["o2"])

    c.add_port("o1", port=wg_bus_ref.ports["o1"])

    c.flatten()
    return c

@gf.cell
def spiral_with_bus(gap: int = 5):
    c = gf.Component()
    # Add spiral and coupler
    spiral = custom_spiral(RR = 2000, RW = 1.9, m_phc = 18000, APhC = 0.1, PhCProfile = None, Dev = 0)
    spiral_ref = c << spiral
    spiral_ref.rotate(90)
    spiral_ref.movex(-10350)
    spiral_ref.movey(10)

    xs = gf.cross_section.cross_section(width=1.9, layer=(2, 0))
    spiral_coupler = gf.components.straight(length=50, cross_section=xs)
    spiral_coupler_ref = c << spiral_coupler
    spiral_coupler_ref.rotate(90)
    spiral_coupler_ref.center = spiral_ref.center
    spiral_coupler_ref.xmin = spiral_ref.xmax + gap 

    bend_s_top = gf.components.bend_s(size=(100, 20), cross_section=xs)
    bend_s_top_ref = c << bend_s_top
    bend_s_top_ref.mirror_y()
    bend_s_top_ref.connect("o1", spiral_coupler_ref.ports["o2"])

    bend_s_bot = gf.components.bend_s(size=(100, 20), cross_section=xs)
    bend_s_bot_ref = c << bend_s_bot
    bend_s_bot_ref.rotate(-90)
    bend_s_bot_ref.connect("o1", spiral_coupler_ref.ports["o1"])
    c.add_port("o1", port=bend_s_top_ref.ports["o2"])
    c.add_port("o2", port=bend_s_bot_ref.ports["o2"])
    c.flatten()
    return c

def load_params_from_excel(filename: str = 'MEngine02.xlsx') -> list[dict]:
    """Loads ring parameters from an Excel file."""
    try:
        df = pd.read_excel(filename)
        array = df.to_numpy()
        r, _ = array.shape
        ring_params = []
        for i in range(44, r):
            params = {
                "radius": array[i, 2],
                "ring_width": array[i, 3],
                "corrugation_amplitude": array[i, 5],
                "wg_width": array[i, 8],
                "gap": array[i, 10],
                "m": array[i, 4],
                "superimpose": array[i, 7],
            }
            ring_params.append(params)
        return ring_params
    except FileNotFoundError:
        print(f"Error: File '{filename}' not found.")
        return []
    except Exception as e:
        print(f"Error reading Excel file: {e}")
        return []
    
if __name__ == "__main__":
    
    c = gf.Component("TOP")
    
    dx, dy = -30, 330
    num_rings = 10
    loaded_ring_params = load_params_from_excel()

    num_arrays = len(loaded_ring_params) // num_rings + 1
    params_for_cell_array = loaded_ring_params[:num_rings*3]
    j, k, delta_x = 0, 0, 0
    port_1, port_2 = [], []
    Top_IO_ports = []
    Bot_IO_ports = []

    # Add chip border
    CHS = gf.components.rectangle(size=(10500, 4850), layer=(100, 0)).dup()
    CSL = gf.components.rectangle(size=(10520, 4870), layer=(100, 2)).dup()
    CSL.move((CHS.center[0] - CSL.size_info.width/2, CHS.center[1] - CSL.size_info.height/2))

    # Import AN IO (black box)
    AN_func = AN350BB_EdgeCoupler_Lensed_C()

    # Create array of rings
    for i in range(len(params_for_cell_array)):

        # Create ring arrays
        current_params = params_for_cell_array[i]
        ring = ring_with_heater(dx = dx, dy = dy, i = i, num_rings = num_rings, **current_params)
        ring_ref = c << ring

        if i % num_rings == 0:
            j = 0
            if i > 0:
                k+=15*dx
            ring_ref.move((k, j*dy +50 + 900))
            
        else:
            ring_ref.move((k, j*dy + 900))
        j+=1
        k+=dx

        # Connect taper to bus waveguide
        if i% num_rings == 0:
            taper_1 = gf.components.taper(
                length=num_rings*dy - 180,
                width1=params_for_cell_array[i]["wg_width"],
                width2=1.0,
                layer=(2, 0),
            )

            taper_2 = gf.components.taper(
                length=100,
                width1=params_for_cell_array[i]["wg_width"],
                width2=1.0,
                layer=(2, 0),
            )

        elif (i + 1) % num_rings == 0:
            taper_1 = gf.components.taper(
                length=80,
                width1=params_for_cell_array[i]["wg_width"],
                width2=1.0,
                layer=(2, 0),
            )

            taper_2 = gf.components.taper(
                length=num_rings*dy - 280,
                width1=params_for_cell_array[i]["wg_width"],
                width2=1.0,
                layer=(2, 0),
            )
        else:
            taper_1 = gf.components.taper(
                length=abs((num_rings - j)*dy) - 100,
                width1=params_for_cell_array[i]["wg_width"],
                width2=1.0,
                layer=(2, 0),
            )

            taper_2 = gf.components.taper(
                length=abs(j*dy) - 480,
                width1=params_for_cell_array[i]["wg_width"],
                width2=1.0,
                layer=(2, 0),
            )
        t_1 = c << taper_1
        t_1.connect("o1", ring_ref.ports["o2"], allow_width_mismatch=True)
        # ring_ref.connect("o2", t_1.ports["o1"])
        t_2 = c << taper_2
        t_2.connect("o1", ring_ref.ports["o1"], allow_width_mismatch=True)
        # ring_ref.connect("o1", t_2.ports["o1"])

        port_1.append(t_1.ports["o2"])
        port_2.append(t_2.ports["o2"])

        # Generate IO
        off_set_IO = 41.5
        if i % num_rings == 0 and i > 0:
            delta_x += -350
            text_ref_top = c << gf.components.text(text= f"{i + 1}", size = 28, layer = (2, 0))
            text_ref_top.rotate(90)
            text_ref_top.move(( -i*off_set_IO + delta_x + 135,CSL.ymax - 430/2))
            text_bot_ref = c << gf.components.text(text= f"{i + 1}", size = 28, layer = (2, 0))
            text_bot_ref.rotate(90)
            text_bot_ref.move(( -i*off_set_IO + delta_x + 135,CSL.ymin + 430/2))

        IO_ref = c << AN_func
        IO_top = IO_ref.rotate(90)
        # IO_top.move(( -i*off_set_IO + delta_x + 100,abs(num_rings*dy) + 100))
        IO_top.movex(( -i*off_set_IO + delta_x + 100))
        IO_top.ymax = CSL.ymax

        # Add overlap waveguide
        overlapp_wg = straight(length=6, width=1.0, layer=(2, 0))
        overlapp_wg_ref = c << overlapp_wg
        overlapp_wg_ref.connect("o1", IO_top.ports["o1"], allow_layer_mismatch=True)
        overlapp_wg_ref.movey(-6)
        Top_IO_ports.append(overlapp_wg_ref.ports["o1"])
        
        IO_ref_2 = c << AN_func
        IO_bot = IO_ref_2.rotate(-90)
        # IO_bot.move(( -i*off_set_IO + delta_x + 100 - 30,dy - 600))
        IO_bot.movex(( -i*off_set_IO + delta_x + 100 - 30))
        IO_bot.ymin = CSL.ymin

        # Add overlap waveguide
        overlapp_wg_ref_2 = c << overlapp_wg.dup()
        overlapp_wg_ref_2.connect("o1", IO_bot.ports["o1"], allow_layer_mismatch=True)
        overlapp_wg_ref_2.movey(6)
        Bot_IO_ports.append(overlapp_wg_ref_2.ports["o1"])

        
    # Connect top IO ports to bus waveguides
    custom_cross_section = gf.cross_section.cross_section(width=1.0, layer=(2, 0))  # Example layer (1, 0)
    routes_1 = gf.routing.route_bundle_sbend(
        c,
        Top_IO_ports, 
        port_1,
        # allow_layer_mismatch=True,
        # allow_width_mismatch=True,
        enforce_port_ordering=False,
        cross_section = custom_cross_section,
    )

    routes_2 = gf.routing.route_bundle_sbend(
        c,
        Bot_IO_ports, 
        port_2,
        allow_layer_mismatch=True,
        # allow_width_mismatch=True,
        enforce_port_ordering=False,
        cross_section = custom_cross_section,
    )

    # Add chip border
    CSL.movex(-10350)
    CHS.movex(-10350)
    c << CSL
    c << CHS

    # Add spiral
    spiral = spiral_with_bus(gap = 2)
    spiral_ref = c << spiral
    spiral_ref.xmin = CHS.xmin
    spiral_ref.ymin = CHS.ymin

    c.move((10410 - 50, 10))
    # c.write_gds("output.gds")
    c.show()

