import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from itertools import product
from scipy.optimize import fsolve, minimize_scalar
import os

class PhCProfileGenerator:
    def __init__(self):
        # Physical parameters
        self.rw = 1.9  # targeted ring width in um
        self.linewidth = 66e-3  # In GHz
        self.D_2_GHz_base = -29.32e-3  # Base dispersion coefficient
        self.m_0 = 1616  # N corrugations at center mode
        
        # Mode numbers (excluding 0)
        self.mu = np.array([i for i in range(-16, 17) if i != 0])
        
        # Sweep parameters
        self.D_2_GHz_relative_var_array = np.array([-1, -0.5, 0, 1]) * 0.04
        self.eps_shift_array = np.arange(4, 11, 2)  # 4:2:10
        self.eps_rel_side_array = np.array([1])
        self.d2_extra_array = np.array([0])  # Could be [-1, 0, 1] * 5e-2
        
        # Calibration polynomial coefficients (placeholder - should be loaded from file)
        self.calibration_poly = None
        
    def load_calibration_data(self, file_path=None):
        """Load calibration polynomial coefficients from Excel file"""
        if file_path and os.path.exists(file_path):
            try:
                df = pd.read_excel(file_path)
                # Find row closest to target ring width
                idx = np.argmin(np.abs(df['RW'] - self.rw))
                self.calibration_poly = df.iloc[idx, 1:].values
                print(f"Loaded calibration for RW = {df.iloc[idx, 0]:.2f} um")
            except Exception as e:
                print(f"Warning: Could not load calibration file: {e}")
                self._use_default_calibration()
        else:
            self._use_default_calibration()
    
    def _use_default_calibration(self):
        """Use default polynomial coefficients if file not available"""
        # Placeholder polynomial coefficients (replace with actual values)
        self.calibration_poly = np.array([1e-6, -1e-4, 5e-3, 0.1, 0])
        print("Using default calibration coefficients")
    
    def create_sweep_table(self):
        """Create parameter sweep combinations"""
        combinations = list(product(
            self.D_2_GHz_relative_var_array,
            self.eps_shift_array,
            self.eps_rel_side_array,
            self.d2_extra_array
        ))
        
        sweep_df = pd.DataFrame(combinations, columns=[
            'D_2_GHz_relative_var', 'eps_shift', 'eps_rel_side', 'd2_extra'
        ])
        
        # Add empty columns for results
        sweep_df['PhCProfile'] = ''
        sweep_df['APhC'] = 0.0
        
        return sweep_df
    
    def calculate_gamma(self, D_2_GHz, eps_shift, eps_rel_side, d2_extra):
        """Calculate gamma (splitting) values for all modes"""
        gamma_shift_GHz = self.linewidth / 2 * 2 * eps_shift
        
        # Main calculation
        gamma_GHz = (np.abs(D_2_GHz) * self.mu**2 + 
                     gamma_shift_GHz + 
                     d2_extra / 2 * self.linewidth / 2 * (self.mu**2 - 1))
        
        # Special cases
        gamma_GHz[np.abs(self.mu) == 1] = eps_rel_side * gamma_shift_GHz
        
        return gamma_GHz
    
    def gamma_to_amplitude(self, gamma_val):
        """Convert gamma to PhC amplitude using polynomial calibration"""
        if gamma_val == 0:
            return 0
        
        if self.calibration_poly is None:
            self.load_calibration_data()
        
        # Solve polynomial equation: P(A) = gamma_val
        poly_shifted = self.calibration_poly.copy()
        poly_shifted[-1] -= gamma_val
        
        roots = np.roots(poly_shifted)
        real_roots = roots[np.isreal(roots)].real
        
        if len(real_roots) > 0:
            return max(real_roots)
        else:
            return 0
    
    def calculate_spatial_profile(self, mu_vals, A_PhC_norm, m_0):
        """Calculate spatial profile with optimized chirp"""
        # Angular positions (0 to pi)
        thi = np.linspace(0, np.pi, round(11 * (m_0 + 2 * max(np.abs(mu_vals)))))
        m_PhC = 2 * mu_vals
        
        def profile_function(C):
            """Profile function with chirp parameter C"""
            profile = np.zeros_like(thi)
            for i, (mu_i, A_i) in enumerate(zip(mu_vals, A_PhC_norm)):
                profile += 0.5 * A_i * np.cos((m_0 + m_PhC[i]) * thi + C * m_PhC[i]**2)
            return profile
        
        def amplitude_variation(C):
            """Total amplitude variation for optimization"""
            profile = profile_function(C)
            return np.max(profile) - np.min(profile)
        
        # Optimize chirp parameter
        result = minimize_scalar(amplitude_variation, bounds=(0, 3), method='bounded')
        optimal_C = result.x
        min_variation = result.fun
        
        return thi, profile_function, optimal_C, min_variation
    
    def generate_profile_name(self, D_2_GHz, eps_shift, eps_rel_side, d2_extra):
        """Generate filename for the profile"""
        return (f'SCC_nullifyDispersion_D2MHz{D_2_GHz*1e3:.1f}_'
                f'KMHz{self.linewidth*1e3:.1f}N{len(self.mu)}_'
                f'epsShift{eps_shift}_epsRelSide{eps_rel_side}_D2Extra{d2_extra}.txt')
    
    def create_plots(self, mu_vals, gamma_GHz, D_2_GHz, A_PhC, thi, profile_funcs, optimal_C, filename_base):
        """Create and save plots"""
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        fig.suptitle(f'PhC Profile Analysis: {filename_base}')
        
        # Plot 1: Gamma vs mode number
        axes[0, 0].plot(mu_vals, gamma_GHz, '.-')
        axes[0, 0].set_ylabel('Γ (GHz)')
        axes[0, 0].set_xlabel('Mode number')
        axes[0, 0].grid(True)
        
        # Plot 2: Dispersion compensation
        eps_GHz = gamma_GHz / 2
        axes[0, 1].plot(mu_vals, D_2_GHz/2 * mu_vals**2, '-', label='Original dispersion')
        axes[0, 1].plot(mu_vals, D_2_GHz/2 * mu_vals**2 + eps_GHz, 'bo', label='Upper band')
        axes[0, 1].plot(mu_vals, D_2_GHz/2 * mu_vals**2 - eps_GHz, 'ro', label='Lower band')
        axes[0, 1].set_ylabel('D_int (GHz)')
        axes[0, 1].set_xlabel('Mode number')
        axes[0, 1].legend()
        axes[0, 1].grid(True)
        
        # Plot 3: Calibration curve
        if self.calibration_poly is not None:
            A_range = np.linspace(0, max(A_PhC)*1e3*1.2, 1000)
            gamma_curve = np.polyval(self.calibration_poly, A_range)
            axes[0, 2].plot(A_range, gamma_curve, '-', label='Calibration')
            axes[0, 2].plot(A_PhC*1e3, gamma_GHz, 'o', label='Design points')
            axes[0, 2].set_xlabel('A_PhC (nm)')
            axes[0, 2].set_ylabel('Total splitting Γ (GHz)')
            axes[0, 2].legend()
            axes[0, 2].grid(True)
        
        # Plot 4: Amplitude vs mode number
        axes[1, 0].plot(mu_vals, A_PhC, 'o')
        axes[1, 0].set_xlabel('Mode number')
        axes[1, 0].set_ylabel('A_PhC (μm)')
        axes[1, 0].grid(True)
        
        # Plot 5: Spatial profiles
        profile_no_chirp = profile_funcs(0) * max(A_PhC)
        profile_optimized = profile_funcs(optimal_C) * max(A_PhC)
        
        axes[1, 1].plot(thi, profile_no_chirp, label='No chirp')
        axes[1, 1].plot(thi, profile_optimized, label=f'Optimized (C={optimal_C:.3f})')
        axes[1, 1].set_xlabel('φ (rad)')
        axes[1, 1].set_ylabel('ρ_PhC (μm)')
        axes[1, 1].legend()
        axes[1, 1].grid(True)
        
        # Plot 6: Phase profile
        m_PhC = 2 * mu_vals
        phases = optimal_C * m_PhC**2
        axes[1, 2].plot(mu_vals, phases, 'o-')
        axes[1, 2].set_xlabel('Mode number')
        axes[1, 2].set_ylabel('Phase (rad)')
        axes[1, 2].grid(True)
        
        plt.tight_layout()
        plt.savefig(f'{filename_base}.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def run_analysis(self, save_plots=True, save_tables=True):
        """Run the complete analysis"""
        # Validation
        if self.m_0 % 2 != 0:
            raise ValueError("m_0 must be even")
        
        # Load calibration
        self.load_calibration_data()
        
        # Create sweep table
        sweep_df = self.create_sweep_table()
        results = []
        
        print(f"Running analysis for {len(sweep_df)} parameter combinations...")
        
        for idx, row in sweep_df.iterrows():
            print(f"Processing combination {idx+1}/{len(sweep_df)}")
            
            # Calculate dispersion coefficient
            D_2_GHz = self.D_2_GHz_base * (1 + row['D_2_GHz_relative_var'])
            
            # Calculate gamma values
            gamma_GHz = self.calculate_gamma(
                D_2_GHz, row['eps_shift'], row['eps_rel_side'], row['d2_extra']
            )
            
            # Convert to amplitudes
            A_PhC = np.array([self.gamma_to_amplitude(g) for g in gamma_GHz])
            A_PhC = np.round(A_PhC, 6)  # Round to nm precision
            A_PhC_max = np.max(A_PhC)
            
            # Normalize amplitudes
            A_PhC_norm = A_PhC / A_PhC_max if A_PhC_max > 0 else A_PhC
            
            # Calculate spatial profile
            thi, profile_func, optimal_C, min_variation = self.calculate_spatial_profile(
                self.mu, A_PhC_norm, self.m_0
            )
            
            # Generate filename
            profile_name = self.generate_profile_name(
                D_2_GHz, row['eps_shift'], row['eps_rel_side'], row['d2_extra']
            )
            filename_base = profile_name.replace('.txt', '')
            
            # Create results table for this configuration
            m_PhC = 2 * self.mu
            phases = optimal_C * m_PhC**2
            
            result_table = pd.DataFrame({
                'mu': self.mu,
                'APhCNorm': A_PhC_norm,
                'phase': phases
            })
            
            # Save individual results
            if save_tables:
                result_table.to_csv(profile_name, index=False)
            
            # Create plots
            if save_plots:
                self.create_plots(
                    self.mu, gamma_GHz, D_2_GHz, A_PhC, thi, 
                    profile_func, optimal_C, filename_base
                )
            
            # Store results
            results.append({
                'index': idx,
                'PhCProfile': filename_base,
                'APhC': A_PhC_max,
                'min_variation': min_variation,
                'optimal_C': optimal_C
            })
            
            # Update sweep table
            sweep_df.loc[idx, 'PhCProfile'] = filename_base
            sweep_df.loc[idx, 'APhC'] = A_PhC_max
        
        # Add additional columns and save final table
        sweep_df['phcsquareness'] = 0
        
        if save_tables:
            output_df = sweep_df[['APhC', 'phcsquareness', 'PhCProfile']].copy()
            output_df.to_excel('temp.xlsx', index=False)
            sweep_df.to_csv('full_sweep_results.csv', index=False)
        
        print("\nAnalysis complete!")
        print(sweep_df)
        
        return sweep_df, results

# Usage example
if __name__ == "__main__":
    generator = PhCProfileGenerator()
    
    # Run the analysis
    sweep_results, detailed_results = generator.run_analysis(
        save_plots=True, 
        save_tables=True
    )
    
    print(f"\nGenerated {len(sweep_results)} profiles")
    print(f"Maximum PhC amplitude range: {sweep_results['APhC'].min():.6f} - {sweep_results['APhC'].max():.6f} μm")