import matplotlib.pyplot as plt
import gdsfactory as gf

# Create our first CrossSection
s0 = gf.Section(width=0.5, offset=0, layer=(1, 0), name="wg", port_names=("o1", "o2"))
s1 = gf.Section(width=0.2, offset=0, layer=(3, 0), name="slab")
x1 = gf.CrossSection(sections=(s0, s1))

# Create the second CrossSection that we want to transition to
s0 = gf.Section(width=2.5, offset=0, layer=(1, 0), name="wg", port_names=("o1", "o2"))
s1 = gf.Section(width=3.0, offset=0, layer=(3, 0), name="slab")
x2 = gf.CrossSection(sections=(s0, s1))

# To show the cross-sections, let's create two Paths and create Components by extruding them
# p1 = gf.path.straight(length=5)
# p2 = gf.path.straight(length=5)
# wg1 = gf.path.extrude(p1, x1)
# wg2 = gf.path.extrude(p2, x2)

# Place both cross-section Components and quickplot them
# c = gf.Component()
# wg1ref = c << wg1
# wg2ref = c << wg2
# wg2ref.dmovex(7.5)

# Create the transitional CrossSection
xtrans = gf.path.transition(cross_section1=x1, cross_section2=x2, width_type="sine")
# Create a Path for the transitional CrossSection to follow
p3 = gf.path.straight(length=15, npoints=100)

# Use the transitional CrossSection to create a Component
straight_transition = gf.path.extrude_transition(p3, xtrans)
s = straight_transition.to_3d()
s.show()

# Create the transitional CrossSection
xtrans = gf.path.transition(
    cross_section1=x1, cross_section2=x2, width_type="parabolic"
)
# Create a Path for the transitional CrossSection to follow
p3 = gf.path.straight(length=15, npoints=100)

# Use the transitional CrossSection to create a Component
straight_transition = gf.path.extrude_transition(p3, xtrans)
straight_transition.plot()

# Create the transitional CrossSection
xtrans = gf.path.transition(cross_section1=x1, cross_section2=x2, width_type="sine")
# Create a Path for the transitional CrossSection to follow
p3 = gf.path.straight(length=15, npoints=100)

# Use the transitional CrossSection to create a Component
straight_transition = gf.path.extrude_transition(p3, xtrans)
straight_transition.plot()

s = straight_transition.to_3d()
s.show()

c = gf.Component()
p1 = gf.kdb.DPolygon([(-8, -6), (6, 8), (7, 17), (9, 5)])  # DPolygons are in um
p2 = p1.sized(2)

c.add_polygon(p1, layer=(1, 0))
c.add_polygon(p2, layer=(2, 0))
m = c.to_3d()
m.show()

def straight(length=10, width=1, layer=(1, 0)):
    c = gf.Component()
    c.add_polygon([(0, 0), (length, 0), (length, width), (0, width)], layer=layer)
    c.add_port(
        name="o1", center=[0, width / 2], width=width, orientation=180, layer=layer
    )
    c.add_port(
        name="o2", center=[length, width / 2], width=width, orientation=0, layer=layer
    )
    return c


c = gf.Component()

wg1 = c << straight(length=6, width=2.5, layer=(1, 0))
wg2 = c << straight(length=6, width=2.5, layer=(2, 0))
wg3 = c << straight(length=15, width=2.5, layer=(3, 0))
wg2.movey(10)
wg2.rotate(10)

wg3.movey(20)
wg3.rotate(15)

c.plot()

# Let's keep wg1 in place on the bottom, and connect the other straights to it.
# To do that, on wg2 we'll grab the "o1" port and connect it to the "o2" on wg1:
wg2.connect("o1", wg1.ports["o2"], allow_layer_mismatch=True)

# Next, on wg3 let's grab the "o1" port and connect it to the "o2" on wg2:
wg3.connect("o1", wg2.ports["o2"], allow_layer_mismatch=True)

c.plot()

c.add_port("o1", port=wg1.ports["o1"])
c.add_port("o2", port=wg3.ports["o2"])

c.draw_ports()
c.plot()

c.pprint_ports()

c = gf.Component()
mmi = c.add_ref(gf.components.mmi1x2())
bend = c.add_ref(gf.components.bend_circular(layer=(1, 0)))
bend.connect("o1", mmi.ports["o1"], mirror=True)  # connects follow Source -> Destination syntax
c.plot()




c = gf.Component()

mmi = c << gf.components.mmi1x2()
b = c << gf.components.bend_circular()
b.connect("o1", other=mmi["o2"])

c.add_port("o1", port=mmi["o1"])
c.add_port("o2", port=b["o2"])
c.add_port("o3", port=mmi["o3"])
c.plot()
print(b.cell.name)

import gdsfactory as gf

bend180 = gf.components.bend_circular180()
wg_pin = gf.components.straight_pin(length=40)
wg = gf.components.straight()

# Define a map between symbols and (component, input port, output port)
symbol_to_component = {
    "D": (bend180, "o1", "o2"),
    "C": (bend180, "o2", "o1"),
    "P": (wg_pin, "o1", "o2"),
    "-": (wg, "o1", "o2"),
}

# Generate a sequence
# This is simply a chain of characters. Each of them represents a component
# with a given input and and a given output

sequence = "DC-P-P-P-P-CD"
component = gf.components.component_sequence(
    sequence=sequence, symbol_to_component=symbol_to_component
)
component.plot()


# Create our first CrossSection
import gdsfactory as gf

s0 = gf.Section(width=1.2, offset=0, layer=(2, 0), name="core", port_names=("o1", "o2"))
s1 = gf.Section(width=2.2, offset=0, layer=(3, 0), name="etch")
s2 = gf.Section(width=1.1, offset=3, layer=(1, 0), name="wg2")
X1 = gf.CrossSection(sections=[s0, s1, s2])

# Create the second CrossSection that we want to transition to
s0 = gf.Section(width=1, offset=0, layer=(2, 0), name="core", port_names=("o1", "o2"))
s1 = gf.Section(width=3.5, offset=0, layer=(3, 0), name="etch")
s2 = gf.Section(width=4.1, offset=6, layer=(1, 0), name="wg2")
X2 = gf.CrossSection(sections=[s0, s1, s2])

# # To show the cross-sections, let's create two Paths and
# # create Components by extruding them
# P1 = gf.path.straight(length=2)
# P2 = gf.path.straight(length=5)
# wg1 = gf.path.extrude(P1, X1)
# wg2 = gf.path.extrude(P2, X2)

# # Place both cross-section Components and quickplot them
# c = gf.Component()
# wg1ref = c << wg1
# wg2ref = c << wg2
# wg2ref.movex(4)

# Create the transitional CrossSection
Xtrans = gf.path.transition(cross_section1=X1, cross_section2=X2, width_type="sine")

# Create a Path for the transitional CrossSection to follow
P3 = gf.path.straight(length=10, npoints=100)

# Use the transitional CrossSection to create a Component
straight_transition = gf.path.extrude_transition(P3, Xtrans)
straight_transition.plot()

from functools import partial


@gf.cell
def mzi_with_bend(mzi=gf.components.mzi, bend=gf.components.bend_euler):
    """Returns MZI interferometer with bend."""
    c = gf.Component()
    mzi1 = c.add_ref(mzi())
    bend1 = c.add_ref(bend())
    bend1.connect("o1", mzi1.ports["o2"])
    c.add_port("o1", port=mzi1.ports["o1"])
    c.add_port("o2", port=bend1.ports["o2"])
    return c


bend_big = partial(gf.components.bend_euler, radius=100)
c = mzi_with_bend(bend=bend_big)
c.plot()

import gdsfactory as gf


P = gf.Path()

straight = gf.path.straight(length=10)
left_turn = gf.path.euler(radius=10, angle=90)
right_turn = gf.path.euler(radius=10, angle=-90)

P.append(straight)
P.append(left_turn)
P.append(right_turn)
P.mirror_y()
P.rotate(90)
cn = P.points

s0 = gf.Section(width=1, offset=0, layer=(1, 0))
X = gf.CrossSection(sections=(s0,))

c = gf.path.extrude(P, X)


c.add_port("o1", center = (cn[0][0], cn[0][1]), width = 1, orientation = 270, layer=(1, 0))
c.add_port("o2", center = (cn[-1][0], cn[-1][1]), width = 1, orientation = 90, layer=(1, 0))
c.draw_ports()
c.plot()



import gdsfactory as gf
import numpy as np

def looploop(num_pts=1000):
    """Simple limacon looping curve."""
    t = np.linspace(-np.pi, 0, num_pts)
    r = 20 + 25 * np.sin(t)
    x = r * np.cos(t)
    y = r * np.sin(t)
    return np.array((x, y)).T


# Create the path points
P = gf.Path()
P.append(gf.path.arc(radius=10, angle=90))
P.append(gf.path.straight())
P.append(gf.path.arc(radius=5, angle=-90))
P.append(looploop(num_pts=1000))
P.rotate(-45)

# Create the crosssection
s0 = gf.Section(width=1, offset=0, layer=(1, 0), port_names=("in", "out"))
s1 = gf.Section(width=0.5, offset=2, layer=(2, 0))
s2 = gf.Section(width=0.5, offset=4, layer=(3, 0))
s3 = gf.Section(width=1, offset=0, layer=(4, 0))
X = gf.CrossSection(sections=(s0, s1, s2, s3))

c = gf.path.extrude(P, X)
c.plot()

import numpy as np
import gdsfactory as gf
from gdsfactory.typings import LayerSpec


## AN IO 
def straight(length=10, width: float = 1, layer=(1, 0)):
    c = gf.Component()
    c.add_polygon([(0, 0), (length, 0), (length, width), (0, width)], layer=layer)
    c.add_port(
        name="o1", center=(0, width / 2), width=width, orientation=180, layer=layer
    )
    c.add_port(
        name="o2", center=(length, width / 2), width=width, orientation=0, layer=layer
    )
    return c


@gf.cell
def AN350BB_EdgeCoupler_Lensed_C(
        
) -> gf.Component:

    c = gf.Component()

    box = straight(length=430, width=30, layer=(13, 2))
    r1 = c << box
    text = c << gf.components.text(text="AN350BB_EdgeCoupler_Lensed_C"
                                   , size = 5, position = (180, 12), layer=(15, 2))
    input = straight(length=5, width=1, layer=(2, 2))
    r2 = c << input
    r2.movey(0,15 - 1/2)
    output = straight(length=10, width=30, layer=(101, 2))
    r3 = c << output 
    r3.movex(430 - 10)
    c.rotate(90)
    c.add_port("o1", port=r2.ports["o1"])
    c.add_port("o2", port=r3.ports["o2"])    
    return c

@gf.cell
def ring_with_bus(
    radius: float = 79.49,
    ring_width: float = 2.2,
    wg_width: float = 1.9,
    lam: float = 0.4655,
    corrugation_amplitude: float = 0.0,
    layer: LayerSpec = (2, 0),
    n_points: int = 7000,
    gap: int = 10,
    dy: int = -240,
    i: int = 14,
) -> gf.Component:
  
    c = gf.Component()

    # Calculate nominal outer and mean inner radii
    R_outer = radius + ring_width / 2
    R_inner_mean = radius - ring_width / 2

    corrugation_periods = round(2*np.pi*R_inner_mean/lam)
    
    if R_inner_mean < corrugation_amplitude:
        # This means the troughs of the corrugation would try to go to a negative radius.
        # min_r_inner = R_inner_mean - corrugation_amplitude
        raise ValueError(
            f"Corrugation amplitude ({corrugation_amplitude}) is too large for the "
            f"inner mean radius ({R_inner_mean:.2f}). "
            "Ensure R_inner_mean >= corrugation_amplitude, otherwise the inner "
            "boundary would self-intersect or become ill-defined."
        )
    
    if corrugation_periods <= 0:
        raise ValueError("Corrugation periods must be a positive integer.")
    
    if n_points < corrugation_periods * 3 and corrugation_periods > 0: # At least 3 points per period
        print(f"Warning: n_points ({n_points}) might be too low for the number of "
              f"corrugation_periods ({corrugation_periods}) to resolve features smoothly. "
              f"Consider increasing n_points to at least {corrugation_periods * 10}.")
    if n_points < 3:
        raise ValueError("n_points must be at least 3.")

    # Angular points for defining the polygon
    # endpoint=False to avoid duplicate point at 0 and 2pi for a closed shape
    theta = np.linspace(0, 2 * np.pi, n_points, endpoint=True)

    # Outer boundary points (circular)
    x_outer = R_outer * np.cos(theta)
    y_outer = R_outer * np.sin(theta)
    outer_points = list(zip(x_outer, y_outer)) # List of (x,y) tuples

    r_inner_modulated = R_inner_mean - corrugation_amplitude * np.sin(corrugation_periods * theta)
    
    # Ensure no part of the inner radius became negative due to numerical precision,
    # though the earlier check (R_inner_mean >= corrugation_amplitude) should prevent this.
    if np.any(r_inner_modulated < -1e-9): # Allow for very small negative due to precision if close to 0
         raise ValueError("Calculated inner modulated radius became unexpectedly negative. "
                          "This should have been caught by prior checks.")
    r_inner_modulated = np.maximum(0, r_inner_modulated) # Clip to 0 if slightly negative due to precision

    x_inner = r_inner_modulated * np.cos(theta)
    y_inner = r_inner_modulated * np.sin(theta)
    inner_points = list(zip(x_inner, y_inner)) # List of (x,y) tuples

    if R_inner_mean == 0 and corrugation_amplitude == 0: # Special case: Filled circle
        # This is essentially gf.components.circle(radius=R_outer, layer=layer, angle_resolution=360/n_points)
        # Using add_polygon with outer_points only.
        polygon = gf.Polygon(outer_points, layer=layer)
        c.add(polygon)
    elif np.all(r_inner_modulated <= 1e-9): # Inner boundary collapsed to the center everywhere
        # This happens if R_inner_mean = corrugation_amplitude AND sin is always 1 (not possible)
        # OR R_inner_mean = 0 and corrugation_amplitude = 0 (handled above)
        # OR R_inner_mean is very small and corrugation makes it effectively zero everywhere.
        # This effectively means it's a filled shape defined by outer_points.
        polygon = gf.Polygon(outer_points, layer=layer)
        c.add(polygon)
    else:
        # Standard case: a ring with a hole (possibly corrugated to the center at points)
        # The polygon points are outer path, then reversed inner path.
        all_polygon_points = outer_points + inner_points[::-1]
        c.add_polygon(all_polygon_points, layer=layer)

    # # Create arbitrary bus waveguide
    P = gf.Path()
    straight = gf.path.straight(length=radius/1.5)
    left_turn = gf.path.euler(radius=radius, angle=90)
    right_turn = gf.path.euler(radius=radius, angle=-90)

    P.append(straight)
    
    if i > 0:
        P.append(left_turn)
        P.append(right_turn)
        P.append(gf.path.straight(length=abs(i*dy)))
        P.mirror_y()
    elif i == 0:
        P.append(gf.path.straight(length=3*radius))

    P.movex(-radius - ring_width/2 - gap - wg_width/2)
    P.movey(-90/2)

    # if i == 14:
    #     P.movex(radius + ring_width/2 + gap + wg_width/2)
    #     P.movey(-90/2)

    P.rotate(90)

    cn = P.points

    s0 = gf.Section(width=wg_width, offset=0, layer=(2, 0))
    X = gf.CrossSection(sections=(s0,))

    d = gf.path.extrude(P, X)
    # d.add_port("o1", center = (cn[0][0], cn[0][1]), width = 1, orientation = 270, layer=(1, 0))
    # d.add_port("o2", center = (cn[-1][0], cn[-1][1]), width = 1, orientation = 90, layer=(1, 0))
    d << c

    # # Create bus waveguide
    # straight_wg = straight(length=radius, width=wg_width, layer=(1, 0))
    # r = c << straight_wg
    # r.rotate(90)
    # r.movex(-radius - ring_width/2 - gap - wg_width/2)
    # r.movey(-radius/2)
    # c.add_port("o1", port=r.ports["o1"])
    # c.add_port("o2", port=r.ports["o2"])
    
    return d


# ring1 = ring_with_curve_bus(gap = 2, corrugation_amplitude= 0.02)
# ring1.show()

@gf.cell
def create_array_of_resonators(
        num_rings: int = 12,
        dx: float = 30,
        dy: float = -120,
        res_params: dict = {}
) -> gf.Component:
    c = gf.Component()

    for i in range(num_rings):
        ring = ring_with_bus(dy = dy, i = i, **res_params)
        ring_ref = c << ring
        ring_ref.move((i*dx, i*dy))
    return c

gf.clear_cache()
array = create_array_of_resonators(num_rings=14, dy = -240, dx = 30
                                   ,res_params={"gap": 2, "corrugation_amplitude": 0.1})
array.draw_ports()
array.show()

import gdsfactory as gf


## AN IO 
def straight(length=10, width: float = 1, layer=(1, 0)):
    c = gf.Component()
    c.add_polygon([(0, 0), (length, 0), (length, width), (0, width)], layer=layer)
    c.add_port(
        name="o1", center=(0, width / 2), width=width, orientation=180, layer=layer
    )
    c.add_port(
        name="o2", center=(length, width / 2), width=width, orientation=0, layer=layer
    )
    return c

@gf.cell
def AN350BB_EdgeCoupler_Lensed_C(
        
) -> gf.Component:

    c = gf.Component()

    box = straight(length=430, width=30, layer=(13, 2))
    r1 = c << box
    text = c << gf.components.text(text="AN350BB_EdgeCoupler_Lensed_C"
                                   , size = 5, position = (180, 12), layer=(15, 2))
    input = straight(length=5, width=1, layer=(2, 2))
    r2 = c << input
    r2.movey(0,15 - 1/2)
    output = straight(length=10, width=30, layer=(101, 2))
    r3 = c << output 
    r3.movex(430 - 10)
    c.rotate(90)
    c.add_port("o1", port=r2.ports["o1"])
    c.add_port("o2", port=r3.ports["o2"])
    c.draw_ports()
    
    return c

gf.clear_cache()
pt = AN350BB_EdgeCoupler_Lensed_C()
pt.draw_ports()
pt.plot()



xs_top = [0, 10, 20, 40, 50, 80]
pitch = 10.0
N = len(xs_top)
xs_bottom = [(i - N / 2) * pitch for i in range(N)]
layer = (2, 0)

top_ports = [
    gf.Port(f"top_{i}", center=(xs_top[i], 0), width=0.5, orientation=270, layer=gf.get_layer(layer))
    for i in range(N)
]

bot_ports = [
    gf.Port(
        f"bot_{i}",
        center=(xs_bottom[i], -300),
        width=0.5,
        orientation=90,
        layer=gf.get_layer(layer),
    )
    for i in range(N)
]

c = gf.Component()
routes = gf.routing.route_bundle_sbend(
    c,
    top_ports,
    bot_ports,
    cross_section="strip",
    layer=(2, 0),
)
c.show()

import numpy as np
import gdsfactory as gf
from gdsfactory.typings import LayerSpec

## AN IO 
def straight(length=10, width: float = 1.9, layer=(1, 0)):
    c = gf.Component()
    c.add_polygon([(0, 0), (length, 0), (length, width), (0, width)], layer=layer)
    c.add_port(
        name="o1", center=(0, width / 2), width=width, orientation=180, layer=layer
    )
    c.add_port(
        name="o2", center=(length, width / 2), width=width, orientation=0, layer=layer
    )
    return c


pt_list = [] # store list of points for bus waveguides from rings to connect with IO ports

@gf.cell
def ring_with_bus(
    radius: float = 79.49,
    ring_width: float = 2.2,
    wg_width: float = 1.9,
    lam: float = 0.4655,
    corrugation_amplitude: float = 0.2,
    layer: LayerSpec = (2, 0),
    n_points: int = 7000,
    gap: int = 10,
    dy: int = -240,
    dx: int = 30,
    i: int = 14,
) -> gf.Component:
  
    c = gf.Component()

    # Calculate nominal outer and mean inner radii
    R_outer = radius + ring_width / 2
    R_inner_mean = radius - ring_width / 2

    corrugation_periods = round(2*np.pi*R_inner_mean/lam)
    
    if R_inner_mean < corrugation_amplitude:
        # This means the troughs of the corrugation would try to go to a negative radius.
        # min_r_inner = R_inner_mean - corrugation_amplitude
        raise ValueError(
            f"Corrugation amplitude ({corrugation_amplitude}) is too large for the "
            f"inner mean radius ({R_inner_mean:.2f}). "
            "Ensure R_inner_mean >= corrugation_amplitude, otherwise the inner "
            "boundary would self-intersect or become ill-defined."
        )
    
    if corrugation_periods <= 0:
        raise ValueError("Corrugation periods must be a positive integer.")
    
    if n_points < corrugation_periods * 3 and corrugation_periods > 0: # At least 3 points per period
        print(f"Warning: n_points ({n_points}) might be too low for the number of "
              f"corrugation_periods ({corrugation_periods}) to resolve features smoothly. "
              f"Consider increasing n_points to at least {corrugation_periods * 10}.")
    if n_points < 3:
        raise ValueError("n_points must be at least 3.")

    # Angular points for defining the polygon
    # endpoint=False to avoid duplicate point at 0 and 2pi for a closed shape
    theta = np.linspace(0, 2 * np.pi, n_points, endpoint=True)

    # Outer boundary points (circular)
    x_outer = R_outer * np.cos(theta)
    y_outer = R_outer * np.sin(theta)
    outer_points = list(zip(x_outer, y_outer)) # List of (x,y) tuples

    r_inner_modulated = R_inner_mean - corrugation_amplitude * np.sin(corrugation_periods * theta)
    
    # Ensure no part of the inner radius became negative due to numerical precision,
    # though the earlier check (R_inner_mean >= corrugation_amplitude) should prevent this.
    if np.any(r_inner_modulated < -1e-9): # Allow for very small negative due to precision if close to 0
         raise ValueError("Calculated inner modulated radius became unexpectedly negative. "
                          "This should have been caught by prior checks.")
    r_inner_modulated = np.maximum(0, r_inner_modulated) # Clip to 0 if slightly negative due to precision

    x_inner = r_inner_modulated * np.cos(theta)
    y_inner = r_inner_modulated * np.sin(theta)
    inner_points = list(zip(x_inner, y_inner)) # List of (x,y) tuples

    if R_inner_mean == 0 and corrugation_amplitude == 0: # Special case: Filled circle
        # This is essentially gf.components.circle(radius=R_outer, layer=layer, angle_resolution=360/n_points)
        # Using add_polygon with outer_points only.
        polygon = gf.Polygon(outer_points, layer=layer)
        c.add(polygon)
    elif np.all(r_inner_modulated <= 1e-9): # Inner boundary collapsed to the center everywhere
        # This happens if R_inner_mean = corrugation_amplitude AND sin is always 1 (not possible)
        # OR R_inner_mean = 0 and corrugation_amplitude = 0 (handled above)
        # OR R_inner_mean is very small and corrugation makes it effectively zero everywhere.
        # This effectively means it's a filled shape defined by outer_points.
        polygon = gf.Polygon(outer_points, layer=layer)
        c.add(polygon)
    else:
        # Standard case: a ring with a hole (possibly corrugated to the center at points)
        # The polygon points are outer path, then reversed inner path.
        all_polygon_points = outer_points + inner_points[::-1]
        c.add_polygon(all_polygon_points, layer=layer)

    # # Create arbitrary bus waveguide
    P = gf.Path()
    straight = gf.path.straight(length=radius/1.5)
    left_turn = gf.path.euler(radius=radius, angle=90)
    right_turn = gf.path.euler(radius=radius, angle=-90)

    # P.append(gf.path.straight( length= abs(14*dy) - abs(dy*i) ))
    P.append(straight)
    
    if i > 0 and i < 13:
        P.append(left_turn)
        P.append(right_turn)
        P.append(gf.path.straight(length=abs(i*dy) - 200))
        P.mirror_y()
    elif i == 0:
        P.append(gf.path.straight(length=3*radius - 200))

    P.rotate(90)
    P.movey(-35)

    if i == 13:
        P.movex(radius + ring_width/2 + gap + wg_width/2)
        P.append(gf.path.straight(length=abs(13*dy) + 2*radius - 200))
    else: 
        P.movex(-radius - ring_width/2 - gap - wg_width/2)

    cn = P.points

    # Exatract points for bus waveguides, pay attention to dx, dy from for loop in create_array_of_resonators
    if i < 13:
        p_1 = [cn[0, 0] + dx*i, cn[0, 1] + dy*i]
        p_2 = [cn[-1, 0] + dx*i, cn[-1, 1] - abs(dy*i)]
    else:
        p_1 = [cn[0, 0] + dx*i + 55, cn[0, 1] + dy*i + 70]
        p_2 = [cn[-1, 0] + dx*i + 55, cn[-1, 1] - abs(dy*i) + 70]

    pt_list.append(p_1)
    pt_list.append(p_2)

    s0 = gf.Section(width=wg_width, offset=0, layer=(1, 0))
    X = gf.CrossSection(sections=(s0,))

    d = gf.path.extrude(P, X)
    # d.add_port("o1", center = (cn[0][0], cn[0][1]), width = 1, orientation = 270, layer=(1, 0))
    # d.add_port("o2", center = (cn[-1][0], cn[-1][1]), width = 1, orientation = 90, layer=(1, 0))
    d << c

    # # Create straight bus waveguide
    # straight_wg = straight(length=radius, width=wg_width, layer=(1, 0))
    # r = c << straight_wg
    # r.rotate(90)
    # r.movex(-radius - ring_width/2 - gap - wg_width/2)
    # r.movey(-radius/2)
    # c.add_port("o1", port=r.ports["o1"])
    # c.add_port("o2", port=r.ports["o2"])

    d.flatten()
    
    return d


# ring1 = ring_with_curve_bus(gap = 2, corrugation_amplitude= 0.02)
# ring1.show()

@gf.cell
def create_array_of_resonators(
        num_rings: int = 12,
        dx: float = 30,
        dy: float = -120,
        res_params: dict = {}
) -> gf.Component:
    c = gf.Component()

    for i in range(num_rings):
        ring = ring_with_bus(dy = dy, i = i, **res_params)
        ring_ref = c << ring
        if i == num_rings -1:
            ring_ref.move((i*dx + 55, i*dy + 70))
        else: 
            ring_ref.move((i*dx, i*dy))
    
    c.flatten()

    return c

@gf.cell
def AN350BB_EdgeCoupler_Lensed_C(
        
) -> gf.Component:

    c = gf.Component()

    box = straight(length=430, width=30, layer=(13, 2))
    r1 = c << box
    text = c << gf.components.text(text="AN350BB_EdgeCoupler_Lensed_C"
                                   , size = 5, position = (160, 12), layer=(15, 2))
    input = straight(length=5, width=1.9, layer=(2, 2))
    r2 = c << input
    r2.movey(0,15 - 1/2)

    input_2 = straight(length=5, width=1.9, layer=(1, 0))

    r2_2 = c << input_2
    r2_2.movey(0,15 - 1/2)
    
    output = straight(length=10, width=30, layer=(101, 2))
    r3 = c << output 
    r3.movex(430 - 10)
    # c.rotate(90)
    c.add_port("o1", port=r2_2.ports["o1"], layer=(1, 0))
    c.add_port("o2", port=r3.ports["o2"])  
    c.flatten()
    return c

# array = create_array_of_resonators(num_rings=14, dy = -240, dx = 30
#                                    ,res_params={"gap": 2, "corrugation_amplitude": 0.1})
# print(np.array(pt_list))

@gf.cell
def AN350_cell(
    cols: int = 7,
    rows: int = 1,
    col_pitch: float = 35,
    row_pitch: float = 1,
    val: int = 7,
     
) -> gf.Component:
    
    c = gf.Component()
    io = AN350BB_EdgeCoupler_Lensed_C()
    io_r1 = io.dup().rotate(90) # rotate before pass to .array function
    io_r2 = io.dup().rotate(-90)

    # Create array of top IO ports
    array_1 = gf.components.containers.array(component=io_r1, columns=cols, rows=rows, 
                                     column_pitch=col_pitch, row_pitch=row_pitch)
    text_arr_1 = gf.components.text(text= f"{val + 7}", size = 30).copy().rotate(90)
    # text_arr_1.movey(90)
    # array_2 = c.add_ref(io, columns=cols, rows=rows, column_pitch=col_pitch, row_pitch=row_pitch)
    array_2 = gf.components.containers.array(component=io_r1, columns=cols, rows=rows, 
                                      column_pitch=col_pitch, row_pitch=row_pitch)
    
    text_arr_2 = gf.components.text(text= f"{val}", size = 30).copy().rotate(90) # copy() to avoid locked in Cell.transform 
    # text_box2 = c.add_polygon(gf.get_padding_points(text_arr_2, default=1), layer=(2, 0))
    D_list = [array_1, text_arr_1, array_2, text_arr_2]
    Top_IO = gf.grid(
        D_list, 
        spacing = (-90,0), 
    )

    # Create array of bottom IO ports
    array_1 = gf.components.containers.array(component=io_r2, columns=cols, rows=rows, 
                                     column_pitch=col_pitch, row_pitch=row_pitch)
    text_arr_1 = gf.components.text(text= f"{val + 7}", size = 30).copy().rotate(-90)
    array_2 = gf.components.containers.array(component=io_r2, columns=cols, rows=rows, 
                                      column_pitch=col_pitch, row_pitch=row_pitch)
    text_arr_2 = gf.components.text(text= f"{val}", size = 30).copy().rotate(-90) # copy() to avoid locked in Cell.transform 
    D_list = [array_1, text_arr_1, array_2, text_arr_2]
    Bot_IO = gf.grid(
        D_list, 
        spacing = (-90,0), 
    )

    # Call array of resonators to connect to IO ports
    array = create_array_of_resonators(num_rings=14, dy = -240, dx = 30
                                   ,res_params={"gap": 2, "corrugation_amplitude": 0.1})
    c << array
    list_pt_1 = pt_list[1::2] # list of points for bus waveguides from rings to connect with IO ports at top
    list_pt_2 = pt_list[0::2] # list of points for bus waveguides from rings to connect with IO ports at bottom

    # Position IO array
    Top_IO.move([list_pt_1[0][0] + 60 , list_pt_1[0][1] + 400])
    c << Top_IO

    Bot_IO.move([list_pt_2[0][0] + 60 , list_pt_2[-1][1] - 800])
    c << Bot_IO

    # Generate top ports for bus wgs 
    bus_Top_ports = [
        gf.Port(
            f"bot_{i}",
            center=(list_pt_1[i][0], list_pt_1[i][1]),
            width=1.9,
            orientation=90,
            layer=gf.get_layer((1, 0)),
        )
        for i in range(len(list_pt_1))
    ]

    Top_IO_ports = gf.port.get_ports_list(Top_IO, layer=(1, 0))

    top_ports = []

    # for i, port in enumerate(ports_IO):
    #     top_ports.append(
    #         gf.Port(
    #             f"top_{i}",
    #             center=port.center,
    #             width=1.9,
    #             orientation=270,
    #             layer=gf.get_layer((1, 0)),
    #         )
    #     )


    # Connect single route to bus waveguide at the bottom
    lp = [(list_pt_2[i][1] + (14 - i)*-240 + 400) for i in range(len(list_pt_2) - 1)]
    lp.append(list_pt_2[-1][1])

    points_port_90 = [
        gf.Port(
            f"sg_{i}",
            center=(list_pt_2[i][0], lp[i]),
            width=1.9,
            orientation=90,
            layer=gf.get_layer((1, 0)),
        )
        for i in range(len(list_pt_2))
    ]

    bot_bus = [
        gf.Port(
            f"bot_{i}",
            center=(list_pt_2[i][0], list_pt_2[i][1]),
            width=1.9,
            orientation=270,
            layer=gf.get_layer((1, 0)),
        )
        for i in range(len(list_pt_2))
    ]

    for i in range(len(list_pt_2)):
        route = gf.routing.route_single(
            c,
            points_port_90[i],
            bot_bus[i],
            # cross_section="strip",
            layer=(1, 0),
            allow_width_mismatch=True,
            auto_taper=False,
            route_width=1.9,
        )

    # Connect bottom bus to IO ports
    points_port_270 = [
        gf.Port(
            f"sg_{i}",
            center=(list_pt_2[i][0], lp[i]),
            width=1.9,
            orientation=270,
            layer=gf.get_layer((1, 0)),
        )
        for i in range(len(list_pt_2))
    ]
       
    routes_1 = gf.routing.route_bundle_sbend(
        c,
        Top_IO_ports, 
        bus_Top_ports,
        allow_layer_mismatch=True,
        allow_width_mismatch=True,
        # use_port_width=False,
        enforce_port_ordering=False,
    )

    Bot_IO_ports = gf.port.get_ports_list(Bot_IO, layer=(1, 0))

    routes_2 = gf.routing.route_bundle_sbend(
        c,
        Bot_IO_ports, 
        points_port_270,
        allow_layer_mismatch=True,
        allow_width_mismatch=True,
        # use_port_width=False,
        enforce_port_ordering=False,
    )

    c.flatten()

    return c

@gf.cell
def AN350_array():

    c = gf.Component()

    # array = gf.components.containers.array(component=AN_cell_copy, columns=7, rows=1, 
                                    #  column_pitch=700, row_pitch=0)
    num_AN_cell = 13

    for i in range(num_AN_cell):
        AN_cell = AN350_cell(val = i*14 + 1)
        AN_cell_copy = AN_cell.dup()
        c << AN_cell_copy.dup().movex(-700*(i+1))
        
    c.flatten()
    return c

if __name__ == "__main__":
    # gf.clear_cache() 
    # io = AN350BB_EdgeCoupler_Lensed_C()
    # k = io.dup().rotate(90)
    k = AN350_array()
    k.draw_ports()
    k.show()

custom_xs = gf.cross_section.strip(width=0.2)  # 2.0 microns wide
c = gf.components.spiral_double(
    separation=1, 
    number_of_loops=2, 
    min_bend_radius=40, 
    cross_section=custom_xs
)
c.draw_ports()
c.show()

cross_section = gf.cross_section.strip(width=0.2)
c = gf.components.bend_s(size=(11, 3), npoints=99, width=0.7, allow_min_radius_violation=False).copy()
st = c.add_ref(gf.components.straight(length=10, width=0.7))
st.connect("o1", c.ports["o1"])
c.draw_ports()
c.plot()

c = gf.components.ring_single(gap=0.2, radius=10, length_x=4, length_y=0.6, bend='bend_euler', straight='straight', coupler_ring='coupler_ring', cross_section='strip').copy()
c.draw_ports()
c.plot()

import gdsfactory as gf

c = gf.components.ring_double(gap=0.2, radius=10, length_x=0.01, length_y=0.01, bend='bend_euler', straight='straight', coupler_ring='coupler_ring', cross_section='strip').copy()
c.draw_ports()
c.plot()

b = gf.Component()

taper = gf.components.taper(length=220, width1=1.9, width2=1.9, layer=(1, 0))
t = b << taper
b.draw_ports()
b.plot()

d = gf.Component()
radius = 79.49
P = gf.Path()
straight = gf.path.straight(length=radius/1.5)
left_turn = gf.path.euler(radius=radius, angle=90)
right_turn = gf.path.euler(radius=radius, angle=-90)

# P.append(gf.path.straight( length= abs(14*dy) - abs(dy*i) ))
P.append(straight)


P.append(left_turn)
P.append(right_turn)
P.append(gf.path.straight(length=abs(3*6) ))
P.mirror_y()


P.rotate(90)
P.movey(-35)

c = gf.path.extrude(P, layer=(1, 0), width=1.5)
d << c
d.add_port("o1", port=c.ports["o1"])
d.add_port("o2", port=c.ports["o2"])
d.draw_ports()
d.plot()

import gdsfactory as gf

c = gf.Component()

sp = c << gf.components.spiral_double(
    separation=1, 
    number_of_loops=2, 
    min_bend_radius=40, 
    bend='bend_circular',
    npoints=1000,
    cross_section="strip",
    # layer=(1, 0)
).copy()
# sp.rotate(90)
# gf.clear_cache()
c.draw_ports()
# c.flatten()
c.pprint_ports()
c.plot()

import gdsfactory as gf

xs  = gf.cross_section.cross_section(width=1.9, layer=(1, 0))

c = gf.components.bend_s_offset(offset=20, cross_section=xs).copy()
c.draw_ports()
c.plot()

import numpy as np
import photonforge as pf
from matplotlib import pyplot as plt



tech = pf.basic_technology(strip_width=0.45)
pf.config.default_technology = tech

lda = np.linspace(1.5, 1.6, 51)
freqs = pf.C_0 / lda



port_spec = pf.config.default_technology.ports["Strip"]

core_width, _ = port_spec.path_profile_for("WG_CORE")

# Set our defaults
pf.config.default_kwargs = {
    "port_spec": port_spec,
    "radius": 3,
    "euler_fraction": 0.5,
}

coupler = pf.parametric.dual_ring_coupler(
    coupling_distance=core_width + 0.1,
    coupling_length=2.5,
    tidy3d_model_kwargs={
        # These symmetries are not correct for arbitrary ports.
        # They can be used in this case for the fundamental mode.
        "port_symmetries": [
            ("P1", "P0", "P3", "P2"),
            ("P2", "P3", "P0", "P1"),
            ("P3", "P2", "P1", "P0"),
        ],
    },
    name="COUPLER",
)
coupler


c = gf.Component()
pt = c << gf.components.pad_array(port_orientation=270, columns=1, centered_ports=False)
pb = c << gf.components.pad_array(port_orientation=90, columns=1, centered_ports=False)
pt.move((300, 300))
route = gf.routing.route_single(
    c,
    pt.ports["e11"],
    pb.ports["e11"],
    bend="wire_corner45",
    port_type="electrical",
    cross_section="metal_routing",
    allow_width_mismatch=True,
)
c.plot()

import gdsfactory as gf

c = gf.components.straight_heater_metal(length=100.0)
cc = gf.routing.add_pads_bot(component=c, port_names=("l_e4", "r_e4"), fanout_length=120)
cc.show()

c = gf.components.bend_circular_heater(angle=90, heater_to_wg_distance=1.2, heater_width=0.5, layer_heater='HEATER', cross_section='strip', allow_min_radius_violation=False).copy()
c.draw_ports()
c.plot()