import numpy as np
import gdsfactory as gf
from gdsfactory.typings import LayerSpec
import pandas as pd

## AN IO 
def straight(length=10, width: float = 1.9, layer=(1, 0)):
    c = gf.Component()
    c.add_polygon([(0, 0), (length, 0), (length, width), (0, width)], layer=layer)
    c.add_port(
        name="o1", center=(0, width / 2), width=width, orientation=180, layer=layer
    )
    c.add_port(
        name="o2", center=(length, width / 2), width=width, orientation=0, layer=layer
    )
    return c


pt_list = [] # store list of points for bus waveguides from rings to connect with IO ports

@gf.cell
def racetrack_coupler(radius: float = 0, 
                      gap: float = 0.65, 
                      wg_width: float = 1.9,
                      coupler_length: float = 10,
                      bend_s_c_length: float = 62,
                      bend_s_c_width: float = 8,
                      corrugation_amplitude: float = 0.2,
                      ring_width: float = 2.2,
                      layer: LayerSpec = (1, 0),
                      **kwargs
                      )-> gf.Component:
    
    c = gf.Component()
    # Create racetrack
    st_1 = gf.components.straight(length=radius, width=ring_width)
    st_1_ref = c << st_1
    st_1_ref.rotate(90)
    st_2_ref = c << st_1.dup()
    bend_1 =  gf.components.bend_euler(radius=radius/2, angle=180, width=ring_width)
    bend_1_ref = c << bend_1
    bend_2_ref = c << bend_1.dup()
    bend_2_ref.mirror_x()
    bend_1_ref.connect("o1", st_1_ref.ports["o2"])
    bend_2_ref.connect("o1", st_1_ref.ports["o1"])
    st_2_ref.connect("o1", bend_2_ref.ports["o2"])

    # Creat coupler
    bend_s_c = gf.components.bend_s(size=(bend_s_c_length, bend_s_c_width), npoints=99, width=wg_width, allow_min_radius_violation=False)
    bend_s_c_ref = c << bend_s_c
    bend_s_c_ref.rotate(90)
    bend_s_c_ref.movex(bend_s_c_width + gap + wg_width/2 + ring_width/2)
    bend_s_c_ref.movey((radius - bend_s_c_length*2 + coupler_length)/2)
    st_c = c << gf.components.straight(length=coupler_length, width=wg_width)
    st_c.connect("o1", bend_s_c_ref.ports["o2"])
    bend_s_c_ref_2 = c << bend_s_c.dup()
    bend_s_c_ref_2.mirror_x()
    bend_s_c_ref_2.connect("o1", st_c.ports["o2"])
    c.add_port("o1", port=bend_s_c_ref.ports["o1"], layer=layer)
    c.add_port("o2", port=bend_s_c_ref_2.ports["o2"], layer=layer)
    c.flatten()
    return c

@ gf.cell
def spiral (ring_width: float = 1.9,
            separation: float = 6,
            number_of_loops: int = 2,
            radius: float = 40,
            **kwargs
            ) -> gf.Component:
    c = gf.Component()

    # Create spiral
    sp = gf.components.spiral_double(
        separation=separation, 
        number_of_loops=number_of_loops, 
        min_bend_radius=radius/6, 
        cross_section=gf.cross_section.strip(width=ring_width, layer=(1, 0)),
    ).dup()
    sp.rotate(90)
    c << sp
    
    c.add_port("o1", port=sp.ports["o1"])
    c.add_port("o2", port=sp.ports["o2"])
    c.flatten()
    return c

@gf.cell
def sprial_and_racetrack(params_for_cells: list[dict]
                         )->gf.Component:
    c = gf.Component()

    delta_top_y = -300
    # Create racetrack 1 and connected wgs
    rt_1 = c << racetrack_coupler(bend_s_c_width=15, bend_s_c_length=62, **params_for_cells[3])
    rt_1.mirror_x()
    rt_1.movey(delta_top_y)
    taper_top_rt_1 = c << gf.components.taper(length=200, width1=params_for_cells[3]["wg_width"], width2=1.9, layer=(1, 0))
    taper_top_rt_1.connect("o1", rt_1.ports["o2"])
    taper_bot_rt_1 = c << gf.components.taper(length=4.6*rt_1.size_info.height, width1=params_for_cells[3]["wg_width"], width2=1.9, layer=(1, 0))
    taper_bot_rt_1.connect("o1", rt_1.ports["o1"])
    # c.add_port("o1", port=taper_top_rt_1.ports["o2"])

    # Create racetrack 2 and connected wgs
    rt_2 = c << racetrack_coupler(bend_s_c_width=40, bend_s_c_length=100, **params_for_cells[4])
    rt_2.mirror_x()
    rt_2.move((50,-50-rt_2.size_info.height + delta_top_y))
    bend_rt_2 = c << gf.components.bend_s_offset(offset=300, radius=125, width=params_for_cells[4]["wg_width"])
    bend_rt_2.mirror_x()
    bend_rt_2.connect("o1", rt_2.ports["o2"])
    taper_top_rt_2 = c << gf.components.taper(length=0.3*rt_2.size_info.height, width1=params_for_cells[4]["wg_width"], width2=1.9, layer=(1, 0))
    taper_top_rt_2.connect("o1", bend_rt_2.ports["o2"])
    taper_bot_rt_2 = c << gf.components.taper(length=3.5*rt_2.size_info.height, width1=params_for_cells[4]["wg_width"], width2=1.9, layer=(1, 0))
    taper_bot_rt_2.connect("o1", rt_2.ports["o1"])

    # Create racetrack 3 and connected wgs
    rt_3 = c << racetrack_coupler(bend_s_c_width=20, bend_s_c_length=100, **params_for_cells[5])
    rt_3.mirror_x()
    rt_3.move((50*1.5,-50*2-2*rt_2.size_info.height+ delta_top_y))
    bend_rt_3 = c << gf.components.bend_s_offset(offset=300, radius=130, width=params_for_cells[5]["wg_width"])
    bend_rt_3.mirror_x()
    bend_rt_3.connect("o1", rt_3.ports["o2"])
    taper_top_rt_3 = c << gf.components.taper(length=1.3*rt_3.size_info.height, width1=params_for_cells[5]["wg_width"], width2=1.9, layer=(1, 0))
    taper_top_rt_3.connect("o1", bend_rt_3.ports["o2"])
    taper_bot_rt_3 = c << gf.components.taper(length=2.45*rt_3.size_info.height, width1=params_for_cells[5]["wg_width"], width2=1.9, layer=(1, 0))
    taper_bot_rt_3.connect("o1", rt_3.ports["o1"])

    # Create spirals
    sp_1 = c << spiral(**params_for_cells[0])
    sp_1.move((50*6 , -50*1.5-2*rt_2.size_info.height - sp_1.size_info.height + delta_top_y))
    taper_bot_sp_1 = c << gf.components.taper(length=1.7*rt_1.size_info.height, width1=params_for_cells[0]["wg_width"], width2=1.9, layer=(1, 0))
    taper_bot_sp_1.connect("o1", sp_1.ports["o1"])
    taper_top_sp_1 = c << gf.components.taper(length=3.5*sp_1.size_info.height, width1=params_for_cells[0]["wg_width"], width2=1.9, layer=(1, 0))
    taper_top_sp_1.connect("o1", sp_1.ports["o2"])

    sp_2 = c << spiral(**params_for_cells[1])
    sp_2.move((50*7, -50*2-2*rt_2.size_info.height - 2*sp_1.size_info.height + delta_top_y))
    taper_bot_sp_2 = c << gf.components.taper(length=1.3*sp_2.size_info.height, width1=params_for_cells[1]["wg_width"], width2=1.9, layer=(1, 0))
    taper_bot_sp_2.connect("o1", sp_2.ports["o1"])
    taper_top_sp_2 = c << gf.components.taper(length=3*rt_2.size_info.height, width1=params_for_cells[1]["wg_width"], width2=1.9, layer=(1, 0))
    taper_top_sp_2.connect("o1", sp_2.ports["o2"])

    sp_3 = c << spiral(**params_for_cells[2]) 
    sp_3.move((50*8, -50*2.5-2*rt_2.size_info.height - 3*sp_1.size_info.height + delta_top_y))
    taper_bot_sp_3 = c << gf.components.taper(length=100, width1=params_for_cells[2]["wg_width"], width2=1.9, layer=(1, 0))
    taper_bot_sp_3.connect("o1", sp_3.ports["o1"])
    taper_top_sp_3 = c << gf.components.taper(length=2200, width1=params_for_cells[2]["wg_width"], width2=1.9, layer=(1, 0))
    taper_top_sp_3.connect("o1", sp_3.ports["o2"])

    # List ports for connection to IO
    ports_1 = [taper_bot_rt_1.ports["o2"], 
                taper_bot_rt_2.ports["o2"],
                taper_bot_rt_3.ports["o2"],
                taper_bot_sp_1.ports["o2"], 
                taper_bot_sp_2.ports["o2"], 
                taper_bot_sp_3.ports["o2"]]
    ports_2 = [
            taper_top_rt_1.ports["o2"],
            taper_top_rt_2.ports["o2"],
            taper_top_rt_3.ports["o2"],
            taper_top_sp_1.ports["o2"],
            taper_top_sp_2.ports["o2"],
            taper_top_sp_3.ports["o2"],
            ]

    # Call IO ports
    io = AN350BB_EdgeCoupler_Lensed_C()
    io_r1 = io.dup().rotate(90) # rotate before pass to .array function
    io_r2 = io.dup().rotate(-90)

    # Create array of top IO ports
    cols = int(6)
    rows = 1
    col_pitch = int(rt_1.size_info.width / 3)
    array_1 = gf.components.containers.array(component=io_r1, columns=cols, rows=rows, 
                                     column_pitch=col_pitch, row_pitch=0)
    text_arr_1 = gf.components.text(text= f"{1}", size = 28).copy().rotate(90)

    D_list = [array_1, text_arr_1]
    Top_IO = gf.grid(
        D_list, 
        spacing = (-200,0), # size of text box is considered equal to array 
    )

    # Bottom IO ports
    array_2 = gf.components.containers.array(component=io_r2, columns=cols, rows=rows, 
                                     column_pitch=col_pitch, row_pitch=0)
    text_arr_2 = gf.components.text(text= f"{1}", size = 28).copy().rotate(-90)

    D_list = [array_2, text_arr_2]
    Bot_IO = gf.grid(
        D_list,     
        spacing = (-200,0), # size of text box is considered equal to array 
    )   

    y = sp_3.center[1]

    y_Top_IO.append(500)
    y_Bot_IO.append(y - Bot_IO.size_info.height - 450)

    Top_IO.move((150, y_Top_IO[0]))
    Bot_IO.move((150, y_Bot_IO[0]))

    c << Bot_IO
    c << Top_IO

    # Connect bottom IO ports to bus waveguides
    Bot_IO_ports = gf.port.get_ports_list(Bot_IO, layer=(1, 0))
    routes_1 = gf.routing.route_bundle_sbend(
        c,
        Bot_IO_ports, 
        ports_1,
        allow_layer_mismatch=True,
        # allow_width_mismatch=True,
        # use_port_width=False,
        enforce_port_ordering=False,
    )

    # Connect top IO ports to bus wg
    Top_IO_ports = gf.port.get_ports_list(Top_IO, layer=(1, 0))
    routes_2 = gf.routing.route_bundle_sbend(
        c,
        Top_IO_ports, 
        ports_2,
        allow_layer_mismatch=True,
        # allow_width_mismatch=True,
        # use_port_width=False,
        enforce_port_ordering=False,
    )

    c.flatten()

    return c

@gf.cell
def ring_with_bus(
    radius: float = 79.49,
    ring_width: float = 2.2,
    wg_width: float = 1.9,
    lam: float = 0.4655,
    corrugation_amplitude: float = 0.2,
    layer: LayerSpec = (1, 0),
    n_points: int = 12000,
    gap: int = 10,
    dy: int = -240,
    dx: int = 30,
    i: int = 14,
    num_rings: int = 14
) -> gf.Component:
  
    c = gf.Component()

    # Calculate nominal outer and mean inner radii
    R_outer = radius + ring_width / 2
    R_inner_mean = radius - ring_width / 2

    corrugation_periods = round(2*np.pi*R_inner_mean/lam)
    
    if R_inner_mean < corrugation_amplitude:
        # This means the troughs of the corrugation would try to go to a negative radius.
        # min_r_inner = R_inner_mean - corrugation_amplitude
        raise ValueError(
            f"Corrugation amplitude ({corrugation_amplitude}) is too large for the "
            f"inner mean radius ({R_inner_mean:.2f}). "
            "Ensure R_inner_mean >= corrugation_amplitude, otherwise the inner "
            "boundary would self-intersect or become ill-defined."
        )
    
    if corrugation_periods <= 0:
        raise ValueError("Corrugation periods must be a positive integer.")
    
    if n_points < corrugation_periods * 3 and corrugation_periods > 0: # At least 3 points per period
        print(f"Warning: n_points ({n_points}) might be too low for the number of "
              f"corrugation_periods ({corrugation_periods}) to resolve features smoothly. "
              f"Consider increasing n_points to at least {corrugation_periods * 10}.")
    if n_points < 3:
        raise ValueError("n_points must be at least 3.")

    # Angular points for defining the polygon
    # endpoint=False to avoid duplicate point at 0 and 2pi for a closed shape
    theta = np.linspace(0, 2 * np.pi, n_points, endpoint=True)

    # Outer boundary points (circular)
    x_outer = R_outer * np.cos(theta)
    y_outer = R_outer * np.sin(theta)
    outer_points = list(zip(x_outer, y_outer)) # List of (x,y) tuples

    r_inner_modulated = R_inner_mean - corrugation_amplitude * np.sin(corrugation_periods * theta)
    
    # Ensure no part of the inner radius became negative due to numerical precision,
    # though the earlier check (R_inner_mean >= corrugation_amplitude) should prevent this.
    if np.any(r_inner_modulated < -1e-9): # Allow for very small negative due to precision if close to 0
         raise ValueError("Calculated inner modulated radius became unexpectedly negative. "
                          "This should have been caught by prior checks.")
    r_inner_modulated = np.maximum(0, r_inner_modulated) # Clip to 0 if slightly negative due to precision

    x_inner = r_inner_modulated * np.cos(theta)
    y_inner = r_inner_modulated * np.sin(theta)
    inner_points = list(zip(x_inner, y_inner)) # List of (x,y) tuples

    if R_inner_mean == 0 and corrugation_amplitude == 0: # Special case: Filled circle
        # This is essentially gf.components.circle(radius=R_outer, layer=layer, angle_resolution=360/n_points)
        # Using add_polygon with outer_points only.
        polygon = gf.Polygon(outer_points, layer=layer)
        c.add(polygon)
    elif np.all(r_inner_modulated <= 1e-9): # Inner boundary collapsed to the center everywhere
        # This happens if R_inner_mean = corrugation_amplitude AND sin is always 1 (not possible)
        # OR R_inner_mean = 0 and corrugation_amplitude = 0 (handled above)
        # OR R_inner_mean is very small and corrugation makes it effectively zero everywhere.
        # This effectively means it's a filled shape defined by outer_points.
        polygon = gf.Polygon(outer_points, layer=layer)
        c.add(polygon)
    else:
        # Standard case: a ring with a hole (possibly corrugated to the center at points)
        # The polygon points are outer path, then reversed inner path.
        all_polygon_points = outer_points + inner_points[::-1]
        c.add_polygon(all_polygon_points, layer=layer)

    # # CREAT BUS WAVEGUIDE WITH CURVE PATH
    P = gf.Path()
    straight = gf.path.straight(length=radius/1.5)
    left_turn = gf.path.euler(radius=radius, angle=90)
    right_turn = gf.path.euler(radius=radius, angle=-90)

    P.append(straight)
    
    if i > 0 and i < num_rings - 1:
        P.append(left_turn)
        P.append(right_turn)
        # P.append(gf.path.straight(length=abs(i*dy) - 200))
        P.mirror_y()
    # elif i == 0:
        # P.append(gf.path.straight(length=3*radius - 200))

    P.rotate(90)
    P.movey(-35)

    if i == num_rings - 1:
        P.movex(radius + ring_width/2 + gap + wg_width/2)
        # P.append(gf.path.straight(length=abs(13*dy) + 2*radius - 200))
    else: 
        P.movex(-radius - ring_width/2 - gap - wg_width/2)

    d = gf.path.extrude(P, layer=(1, 0), width=wg_width)
    wg_bus = c << d
    c.add_port("o1", port=wg_bus.ports["o1"])
    c.add_port("o2", port=wg_bus.ports["o2"])
    c.flatten()
    return c

@gf.cell
def create_array_of_resonators(
        num_rings: int = 12,
        dx: float = 30,
        dy: float = -120,
        params_for_cell_array: list[dict] = None
) -> gf.Component:
    c = gf.Component()

    for i in range(num_rings):
        current_params = params_for_cell_array[i]
        # print(current_params)
        ring = ring_with_bus(dy = dy, i = i, num_rings = num_rings, **current_params)
        ring_ref = c << ring
        

        if i == num_rings -1:
            ring_ref.move((i*dx + 55, i*dy + 70))
        else: 
            ring_ref.move((i*dx, i*dy))
        
        c.add_port(f"o1_{i}", port=ring_ref.ports["o1"])
        c.add_port(f"o2_{i}", port=ring_ref.ports["o2"])
    
    c.flatten()

    return c

@gf.cell
def AN350BB_EdgeCoupler_Lensed_C(
        
) -> gf.Component:

    c = gf.Component()

    box = straight(length=430, width=30, layer=(13, 2))
    r1 = c << box
    text = c << gf.components.text(text="AN350BB_EdgeCoupler_Lensed_C"
                                   , size = 5, position = (160, 12), layer=(15, 2))
    input = straight(length=5, width=1.9, layer=(2, 2))
    r2 = c << input
    r2.movey(0,15 - 1/2)

    input_2 = straight(length=5, width=1.9, layer=(1, 0))

    r2_2 = c << input_2
    r2_2.movey(0,15 - 1/2)
    
    output = straight(length=10, width=30, layer=(101, 2))
    r3 = c << output 
    r3.movex(430 - 10)
    # c.rotate(90)
    c.add_port("o1", port=r2_2.ports["o1"], layer=(1, 0))
    c.add_port("o2", port=r3.ports["o2"])  
    c.flatten()
    return c


@gf.cell
def AN350_cell(
    col_pitch: float = 35,
    row_pitch: float = 1,
    ring_val: int = 7,
    num_rings: int = 14,
    params_for_cell_array: list[dict] = None
) -> gf.Component:
    
    c = gf.Component()
    io = AN350BB_EdgeCoupler_Lensed_C()
    io_r1 = io.dup().rotate(90) # rotate before pass to .array function
    io_r2 = io.dup().rotate(-90)

    # Create array of top IO ports
    cols = int(num_rings/2)
    rows = 1
    array_1 = gf.components.containers.array(component=io_r1, columns=cols, rows=rows, 
                                     column_pitch=col_pitch, row_pitch=row_pitch)
    text_arr_1 = gf.components.text(text= f"{ring_val + cols}", size = 28).copy().rotate(90)
    # text_arr_1.movey(90)
    # array_2 = c.add_ref(io, columns=cols, rows=rows, column_pitch=col_pitch, row_pitch=row_pitch)
    array_2 = gf.components.containers.array(component=io_r1, columns= num_rings - cols, rows=rows, 
                                      column_pitch=col_pitch, row_pitch=row_pitch)
    
    text_arr_2 = gf.components.text(text= f"{ring_val}", size = 28).copy().rotate(90) # copy() to avoid locked in Cell.transform 

    if num_rings < 14: # Last rings
        array = gf.components.containers.array(component=io_r1, columns=num_rings, rows=rows, 
                                     column_pitch=col_pitch, row_pitch=row_pitch)
        D_list = [array, text_arr_2]
        Top_IO = gf.grid(
            D_list, 
            spacing = (-5*num_rings,0), # size of text box is considered equal to array 
        )
    else:
        D_list = [array_1, text_arr_1, array_2, text_arr_2]
        Top_IO = gf.grid(
            D_list, 
            spacing = (-6.5*num_rings,0), # size of text box is considered equal to array 
        )

    # Create array of bottom IO ports
    array_1 = gf.components.containers.array(component=io_r2, columns=cols, rows=rows, 
                                     column_pitch=col_pitch, row_pitch=row_pitch)
    text_arr_1 = gf.components.text(text= f"{ring_val + cols}", size = 28).copy().rotate(-90)
    array_2 = gf.components.containers.array(component=io_r2, columns=num_rings - cols, rows=rows, 
                                      column_pitch=col_pitch, row_pitch=row_pitch)
    text_arr_2 = gf.components.text(text= f"{ring_val}", size = 28).copy().rotate(-90) # copy() to avoid locked in Cell.transform 
    
    if num_rings < 14: # Last rings
        array = gf.components.containers.array(component=io_r2, columns=num_rings, rows=rows, 
                                     column_pitch=col_pitch, row_pitch=row_pitch)
        D_list = [array, text_arr_2]
        Bot_IO = gf.grid(
            D_list, 
            spacing = (-5*num_rings,0), # size of text box is considered equal to array 
        )
    else:
        D_list = [array_1, text_arr_1, array_2, text_arr_2]
        Bot_IO = gf.grid(
            D_list, 
            spacing = (-6.5*num_rings,0), # size of text box is considered equal to array
        )

    # Call array of resonators to connect to IO ports
    array = create_array_of_resonators(num_rings=num_rings, dy = -240, dx = 30
                                   ,params_for_cell_array = params_for_cell_array)
    array_ref = c << array

    # extract ports o1_i only from array
    array_ports = array.get_ports_list(layer=(1, 0))
    bus_bot_ports = [port for port in array_ports if port.name.startswith("o1")]
    bus_top_ports = [port for port in array_ports if port.name.startswith("o2")]
    

    # Position IO array
    if len(x_Top_IO) == 0:

        x_Top_IO.append(bus_top_ports[0].center[0] + 60)
        # y_Top_IO.append(bus_top_ports[0].center[1] + 400)

    Top_IO.move([x_Top_IO[0], y_Top_IO[0]])
    c << Top_IO

    if len(x_Bot_IO) == 0:
        x_Bot_IO.append(bus_bot_ports[0].center[0] + 60)
        # y_Bot_IO.append(bus_bot_ports[-1].center[1] - 800)

    Bot_IO.move([x_Bot_IO[0], y_Bot_IO[0]])
    c << Bot_IO

    # Connect top IO ports to bus waveguides
    Top_IO_ports = gf.port.get_ports_list(Top_IO, layer=(1, 0))
    ports_1 = []
    for i in range(len(bus_top_ports)):
        if i == 0:
            taper = gf.components.taper(
                length=220,
                width1=params_for_cell_array[i]["wg_width"],
                width2=1.9,
                layer=(1, 0),)
        elif i < num_rings - 1:
            taper = gf.components.taper(
                length=abs(i*-240) - 200,
                width1=params_for_cell_array[i]["wg_width"],
                width2=1.9,
                layer=(1, 0),
            )
        else:
            taper = gf.components.taper(
                length=abs(i*-240),
                width1=params_for_cell_array[i]["wg_width"],
                width2=1.9,
                layer=(1, 0),
            )
        t = c << taper
        t.connect("o1", bus_top_ports[i])

        # save list of port for connection to IO
        ports_1.append(t.ports["o2"])
    
    

    routes_1 = gf.routing.route_bundle_sbend(
        c,
        Top_IO_ports, 
        ports_1,
        allow_layer_mismatch=True,
        # allow_width_mismatch=True,
        # use_port_width=False,
        enforce_port_ordering=False,
    )

    # Connect bottom IO ports to bus waveguides
    Bot_IO_ports = gf.port.get_ports_list(Bot_IO, layer=(1, 0))
    ports_2 = []
    
    for i in range(len(bus_bot_ports)):

        if i < num_rings - 1:
            taper_2 = gf.components.taper(
                length=abs((num_rings - i)*-240) - 200,
                width1=params_for_cell_array[i]["wg_width"],
                width2=1.9,
                layer=(1, 0),
            )
            
        else:
            taper_2 = gf.components.taper(
                length=150,
                width1=params_for_cell_array[i]["wg_width"],
                width2=1.9,
                layer=(1, 0),
            )

        t = c << taper_2
        t.connect("o1", bus_bot_ports[i], allow_width_mismatch=True)
        ports_2.append(t.ports["o2"])

    routes_2 = gf.routing.route_bundle_sbend(
        c,
        Bot_IO_ports, 
        ports_2,
        allow_layer_mismatch=True,
        # allow_width_mismatch=True,
        # use_port_width=False,
        enforce_port_ordering=False,
    )

    c.flatten()

    return c

@gf.cell
def AN350_array(all_individual_ring_data: list[dict]):

    c = gf.Component()

    # First 2 racetracks and 3 spirals
    sp_rt = c << sprial_and_racetrack(params_for_cells = all_individual_ring_data[:6])
    sp_rt.movex(-100)


    num_AN_cell = 14
    rings_per_cell = 14

    for i in range(num_AN_cell):
        start_index = 6 + i * rings_per_cell
        end_index = start_index + rings_per_cell
        ring_params_for_cell = all_individual_ring_data[start_index:end_index]

        AN_cell = AN350_cell(ring_val = 6+i*rings_per_cell + 1, params_for_cell_array = ring_params_for_cell,
        num_rings = rings_per_cell)

        AN_cell_copy = AN_cell.dup()
        c << AN_cell_copy.dup().movex(-700*(i+1))

    # Last rings
    ring_params_for_cell = all_individual_ring_data[start_index + rings_per_cell:]
    AN_cell = AN350_cell(ring_val = start_index + rings_per_cell + 1, params_for_cell_array = ring_params_for_cell,
    num_rings = 5)
    AN_cell_copy = AN_cell.dup()
    c << AN_cell_copy.dup().movex(-700*(i****))
   
    c.flatten()
    return c

def load_params_from_excel() -> list[dict]:
    ring_params = []
    df = pd.read_excel('LGT-MPW-AN350-01_MEngine01_withSimValues.xlsx')
    array = df.to_numpy()
    r, c = array.shape

    for i in range(r):
        params = {
            "radius": array[i, 3],
            "ring_width": array[i, 4],
            "corrugation_amplitude": array[i, 6],
            "wg_width": array[i, 9],
            "gap": array[i, 11]
        }
        ring_params.append(params)

    return ring_params

if __name__ == "__main__":
    # gf.clear_cache() 

    # Postioning IO ports
    x_Top_IO = []
    y_Top_IO = []
    x_Bot_IO = []
    y_Bot_IO = []

    loaded_ring_params = load_params_from_excel()
    k = AN350_array(all_individual_ring_data = loaded_ring_params)

    k.show()

    
   
