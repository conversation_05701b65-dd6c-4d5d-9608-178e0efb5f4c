
from gdsfactory.typings import Layer
from gdsfactory.technology.layer_map import LayerMap


class LayerMapFab(LayerMap):
    ALLB_: Layer = (0, 1)
    CELLNAME_: Layer = (15, 2)
    CELLSIZE_: Layer = (13, 2)
    CHS_: Layer = (100, 0)
    CSL_: Layer = (100, 2)
    EXCPT_: Layer = (0, 3)
    FPIN_: Layer = (101, 2)
    LC1_: Layer = (50, 0)
    LC2_: Layer = (51, 0)
    LCS_: Layer = (60, 0)
    M1B_: Layer = (40, 1)
    M1PAD_: Layer = (41, 0)
    M1PIN_: Layer = (40, 2)
    M1P_: Layer = (40, 0)
    P1B_: Layer = (30, 1)
    P1PAD_: Layer = (33, 0)
    P1PIN_: Layer = (30, 2)
    P1P_: Layer = (30, 0)
    P1RB_: Layer = (32, 1)
    P1R_: Layer = (32, 0)
    RIBA_: Layer = (10, 4)
    RIBC_: Layer = (10, 0)
    TRN_: Layer = (100, 4)
    VIA_: Layer = (31, 0)
    X1B_: Layer = (2, 1)
    X1PIN_: Layer = (2, 2)
    X1P_: Layer = (2, 0)
    X2B_: Layer = (20, 1)
    X2PIN_: Layer = (20, 2)
    X2P_: Layer = (20, 0)


LAYER = LayerMapFab

