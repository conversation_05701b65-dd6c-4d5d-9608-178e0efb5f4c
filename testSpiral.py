import gdsfactory as gf
import numpy as np
import pandas as pd
import pyclothoids
import matplotlib.pyplot as plt
from scipy.interpolate import CubicSpline


from gdsfactory.technology import (
    LayerMap,
)
from gdsfactory.typings import LayerSpec


SiNMAIN = (2,0)

R_min = 100
overSamplingFactPhC = 24



########################################################################################################################
def transitionS(x_0, y_0, x_1, y_1, t0=0.0, k0=0.0, t1=0.0, k1=0.0, Dmax=0, dmax=0, pts_per_um=2):
    clothoid_list = pyclothoids.SolveG2(x_0, y_0, np.deg2rad(t0), k0, x_1, y_1, np.deg2rad(t1), k1, Dmax=Dmax, dmax=dmax)
    xs = np.array([])
    ys = np.array([])
    for i, clth in enumerate(clothoid_list):
        n_pts = 1 + pts_per_um * np.round(np.max([5, clth.Parameters[-1]]))
        pts = clth.SampleXY(int(n_pts))
        if i == 0:
            idx_start = 0
        else:
            idx_start = 1
        xs = np.append(xs, pts[0][idx_start::])
        ys = np.append(ys, pts[1][idx_start::])

    points = np.array((xs, ys)).T

    return points

    
def custom_spiral_path(num_turns=5, out_angle=90, gap=1, inner_gap=2, num_pts=10000):
    # Establishing number of turns in each arm
    if num_turns <= 1:
        raise ValueError("num_turns must be greater than 1")
    out_angle = np.deg2rad(out_angle)

    # Establishing relevant angles and spiral/centre arc parameters
    a1 = np.pi / 2
    a2 = np.array([np.pi * num_turns + a1, np.pi * num_turns + a1 + out_angle])
    a = inner_gap / 2 - gap / 2
    b = gap / np.pi
    Rc = inner_gap * np.sqrt(1 + (b / (a + b * a1)) ** 2) / 4
    theta = np.degrees(2 * np.arcsin(inner_gap / 4 / Rc))

    # Establishing number of points in each arm
    #s_centre = Rc * np.radians(theta)
    #s_spiral = ((a + a2 * b) ** 2 + b**2) ** (3 / 2) / (3 * (a * b + (a2 * b**2))) # Spiral length ??
    #z = num_pts / (s_spiral[0] + s_spiral[1] + 2 * s_centre)
    #num_pts0 = int(z * s_centre)
    #num_pts1 = int(z * s_spiral[0])
    #num_pts2 = int(z * s_spiral[1]) - num_pts1

    # Forming both spiral arms
    num_pts1 = round(np.abs(a1-a2[0])/np.pi * 900)
    arm1 = np.linspace(a1, a2[0], num_pts1)
    arm2 = np.linspace(a2[0], a2[1], round(np.abs(a2[0]-a2[1])/np.pi * 900))[1:]
    a_spiral = [arm1, np.concatenate([arm1, arm2])]
    r_spiral = [a + b * a_spiral[0], a + b * a_spiral[1]]
    x_spiral = [np.zeros(num_pts1), np.zeros(len(a_spiral[1]))] # Initialize
    y_spiral = [np.zeros(num_pts1), np.zeros(len(a_spiral[1]))]
    for i in range(2):
        x_spiral[i] = r_spiral[i] * np.cos(a_spiral[i])
        y_spiral[i] = r_spiral[i] * np.sin(a_spiral[i])

    x_spiral[1] = -np.flip(x_spiral[1])
    y_spiral[1] = -np.flip(y_spiral[1])
    r_spiral[1] = -np.flip(r_spiral[1])
    a_spiral[1] = np.flip(a_spiral[1])
    start_angle = np.arctan2(y_spiral[1][-1] - y_spiral[1][-2], x_spiral[1][-1] - x_spiral[1][-2]) / np.pi * 180

    # Forming centre arcs
    # pts = _rotate_points(arc(Rc, theta, 360 * num_pts0 / theta).points, -theta / 2 + 90)
    d_max_param = 0
    Dmax_param = 0
    pts1 = transitionS(x_spiral[1][-1], y_spiral[1][-1],
                       0, 0,
                       t0=start_angle, t1=0,
                       k0=1/r_spiral[1][-1], k1=0,
                       dmax=d_max_param, Dmax=Dmax_param)
    pts2 = transitionS(0, 0,
                       x_spiral[0][0], y_spiral[0][0],
                       t0=0, t1=start_angle,
                       k0=0, k1=1/r_spiral[0][0],
                       dmax=d_max_param, Dmax=Dmax_param)

    pts = np.concatenate([pts1[:-1], pts2])
    x_centre = pts[:, 0]
    y_centre = pts[:, 1]

    # Combining into final spiral
    x = np.concatenate([x_spiral[1][:-1], x_centre[:-1], x_spiral[0]]) # reject first and last points of centre
    y = np.concatenate([y_spiral[1][:-1], y_centre[:-1], y_spiral[0]])
    points = np.array((x, y)).T

    P = gf.Path()
    # Manually add points & adjust start and end angles
    P.points = points
    nx1, ny1 = points[1] - points[0]
    P.start_angle = np.arctan2(ny1, nx1) / np.pi * 180 # Rounding helps to get thing connected
    nx2, ny2 = points[-1] - points[-2]
    P.end_angle = np.arctan2(ny2, nx2) / np.pi * 180

    return P, r_spiral


@gf.cell
def custom_spiral(RR, RW, m_phc, APhC, PhCProfile, Dev):
    LR = 2*np.pi * RR
    R_bend = 2*R_min # smallest radius in the center
    gap = RW + 4
    N_turns = 0.5 * (np.sqrt(R_min**2 + gap/2*LR/np.pi) - R_min)/(gap/2)
    delta_path = LR
    straight_corr = 0
    n_iter = 0
    while np.abs(delta_path)/LR > 1e-6:
        spiral, r_spiral = custom_spiral_path(num_turns=N_turns, gap=gap, inner_gap=2*R_bend, num_pts=int(np.round(LR)))
        R_spiral_max = r_spiral[0][-1]
        R_spiral_min = r_spiral[0][0]
        spiral_transition_in = transitionS(
            0, 0, R_spiral_max, -R_spiral_max - gap,
            t0=0, t1=-90,
            k0=0, k1=-1 / R_spiral_max
        )

        theta_out = np.arccos((R_spiral_max - R_min)/(R_spiral_max + R_min + gap))
        angle_out = np.rad2deg(theta_out)
        straight_dist = (R_spiral_max + R_min) * np.sin(theta_out) - R_min/2 + gap/2 + straight_corr

        loop_transition = np.flipud(transitionS(
            R_min, -R_min/2,
            R_min*np.cos(theta_out), R_min*np.sin(theta_out),
            t0=90, t1=90+angle_out,
            k0=0, k1=1/R_min
        ))
        loop = gf.path.arc(radius=R_min, angle=-180, npoints=1800)

        P = gf.Path()
        P.append([
            loop,
            loop_transition
        ])
        rotate_angle = P.end_angle
        P.append([
            gf.path.straight(straight_dist),
            spiral_transition_in,
            spiral
        ])
        P.rotate(-rotate_angle)
        P.center = (0,0)
        P.ymax = 0

        points_list = P.points
        transition_out = transitionS(
            points_list[-1, 0], points_list[-1, 1],
            points_list[0, 0], points_list[0, 1],
            t0=180, t1=angle_out - 180,
            k0=1 / R_spiral_max, k1=-1 / R_min,
            dmax=0, Dmax=100
        )

        points_list = np.concatenate([points_list[0:-1, :], transition_out])
        P = gf.Path(points_list)
        P.start_angle = -(180-angle_out)
        P.end_angle = -(180 - angle_out)

        L_path = P.length()
        delta_path = LR - L_path
        n_iter += 1
        delta_N_turns = delta_path/np.pi * 1/(2*R_spiral_max) * 1/(1+0.05*n_iter)
        print(delta_N_turns)
        if np.abs(delta_N_turns) > 1e-3:
            N_turns += delta_N_turns
        else:
            straight_corr += delta_path/10
        print('Iter %i, Snail resonator gf.Path length %g (%g turns) and %g designed, difference is %g' % (n_iter, L_path, N_turns, LR, delta_path))

    # Now let's deal with the PhC case

    m0_PhC = np.round(m_phc)

    N_per_seg = 2000 # Number of points in each segment for subdivided path

    element = gf.Component('Custom Spiral')

    if not(APhC == 0 or np.isnan(m0_PhC)):

        # _________________________________
        # Check if it's a multiPhC
        if isinstance(PhCProfile, str):
            print("Dev %i: Multi-PhC snail" % Dev)
            # read and copy (in backup) the profile file and deals w/ complex numbers
            PhCFileRoot = './DesignsTables/MultiPhCProfiles/'
            PhCFile = PhCProfile + '.txt'
            PhCProfile = pd.read_csv(PhCFileRoot + PhCFile)
            mPhC = 2 * np.array(PhCProfile['mu']) + m0_PhC
            APhC_norm_array = np.array(PhCProfile['APhCNorm'])
            PhCPhase = np.array(PhCProfile['phase'])
            n_interp = overSamplingFactPhC * np.max(mPhC) + 1
            def wgModFun(t):
                PhC_profile = RW/2 + 0.5 * APhC * np.sum( APhC_norm_array * np.cos(2*np.pi * np.outer(mPhC, t).T + mPhC*PhCPhase), axis=1)
                return PhC_profile

        # _________________________________
        # Simple PhC
        else:
            print("Dev %i: Single PhC snail" % Dev)
            n_interp = overSamplingFactPhC * m0_PhC + 1
            def wgModFun(t):
                PhC_profile = RW/2 + 0.5 * APhC * np.cos(2*np.pi * m0_PhC * t)
                return PhC_profile

        # We need to interpolate the points to have a sufficient resolution
        print('Detected PhC on spiral resonator, interpolating for appropriate resolution ...')

        points_list = P.points
        s, K = P.curvature()
        s = np.insert(s, 0, 0)
        interpolator = CubicSpline(s, points_list, axis=0)
        s_interp = np.linspace(0, s[-1], int(n_interp)) # Interpolate the path to have sufficient resolution for the PhC
        points_list = interpolator(s_interp)

        P = gf.Path(points_list)
        P.start_angle = -(180 - angle_out)
        P.end_angle = -(180 - angle_out)

        P_outer = P.copy()
        P_outer.offset(offset=-RW / 2)

        P.offset(offset=wgModFun)

        points_idx = range(len(points_list))
        points_idx = np.array_split(points_idx, len(points_idx) // N_per_seg + (len(points_idx) % N_per_seg != 0))

        for seg_idx, segment_idx in enumerate(points_idx):
            if seg_idx < len(points_idx) - 1:
                segment_idx = np.append(segment_idx, points_idx[(seg_idx + 1)][0])
            outer_idx = np.rint(np.linspace(segment_idx[0], segment_idx[-1], round(N_per_seg/overSamplingFactPhC))).astype(int)
            pt_list = np.array(np.concatenate((P.points[segment_idx, :], np.flip(P_outer.points[outer_idx, :], axis=0)))).T
            pt_list = list(zip(pt_list[:][0], pt_list[:][1]))
            element.add_polygon(pt_list, layer=SiNMAIN)

    else:
        points_list = P.points
        points_list = np.array_split(points_list, len(points_list) // N_per_seg + (len(points_list) % N_per_seg != 0))

        for seg_idx, segment_points in enumerate(points_list):
            if seg_idx < len(points_list) - 1:
                segment_points = np.vstack((segment_points, points_list[(seg_idx + 1)][0, :]))

            P = gf.Path(segment_points)
            if seg_idx == 0:
                P.start_angle = -(180 - angle_out)
            else:
                P.start_angle = outAngle
            if seg_idx == len(points_list) - 1:
                P.end_angle = -(180 - angle_out)
            outAngle = P.end_angle

            wg_sec = P.extrude(layer=SiNMAIN, width=RW)
            wg_sec = element.add_ref(wg_sec)
            element.absorb(wg_sec)



    element.mirror((1, 0))
    element.movex(straight_dist/4)
    element.ymax = RW/2
    #element.polygons[0].fracture(max_points=10000, precision=0.0005)
    return element


# device = custom_spiral(
#     Dev = 0,
#     RR = 2000,
#     RW = 1.9,
#     m_phc = 18000,
#     APhC = 0.1,
#     PhCProfile = None)
# device.show()