from __future__ import division, print_function, absolute_import
import os
import sys
import argparse
import matplotlib
import pylab as p
from matplotlib import pyplot as plt
import numpy as np
from scipy.interpolate import CubicSpline
from scipy.ndimage import maximum_filter, minimum_filter, uniform_filter
from scipy import signal
import pyclothoids
import warnings
import pandas as pd
from phidl import Path, CrossSection, Device, Group, Layer, Port, quickplot
import phidl.geometry as pg
import phidl.path as pp
import phidl.routing as pr
import yaml
import re
import shutil
from itertools import chain

pd.options.mode.chained_assignment = None  # default='warn'
#matplotlib.use('TkAgg',force=True)

loader = yaml.SafeLoader
loader.add_implicit_resolver(
    u'tag:yaml.org,2002:float',
    re.compile(u'''^(?:
     [-+]?(?:[0-9][0-9_]*)\\.[0-9_]*(?:[eE][-+]?[0-9]+)?
    |[-+]?(?:[0-9][0-9_]*)(?:[eE][-+]?[0-9]+)
    |\\.[0-9_]+(?:[eE][-+][0-9]+)?
    |[-+]?[0-9][0-9_]*(?::[0-5]?[0-9])+\\.[0-9_]*
    |[-+]?\\.(?:inf|Inf|INF)
    |\\.(?:nan|NaN|NAN))$''', re.X),
    list(u'-+**********.'))

######################
# Run configuration file
runConfigFile = 'Rtest.yaml'
parser = argparse.ArgumentParser(
    description='Assembling resonators descriptors files to GDS')
parser.add_argument('inputFile', nargs='?', default=runConfigFile,
                    help='yaml configuration file in RunData, default: %s' % runConfigFile)
parser.add_argument('--svg', action="store_true",
                    help='Enables export of resonators as svg')
parser.add_argument('--subGDS', action="store_true",
                    help='Enables export of individual resonators as gds with an additional file for port configuration')
parser.add_argument('--exportProfiles', action="store_true",
                    help='Enables export of individual resonators MultiPhC profiles as figures')
args = parser.parse_args()
runConfigFile = args.inputFile


# Read the run description file
with open('./RunData/' + runConfigFile) as f:
    runInfo = yaml.load(f, Loader=loader)

######################
# Extra 'baked-in' placement and dimensions parameters

#fontName = './LLDOT.ttf'
fontName = './SubwayTickerGrid.ttf'
labelsFontSize = 35
chipFontSize = labelsFontSize  # size of text of chip name

overSamplingFactPhC = 24
overSamplingFactPhC = 2*round(overSamplingFactPhC/2) # Force that to be an even number

R_min = 100
d_safe = 5 # Safe distance between the ring and bus

# Layers
SiNMAIN = (2,0)
ALLB = (0,1)
EXCPT = (0,3)
CHIP = (100,0)
AREA = (100,2)
TEXT = SiNMAIN#(40,0)
CELLSIZE=(13,2)
CELLNAME = (15,2)

######################
# Create the run folder
pathName = './RunData/%s' % (str(runInfo['runName']))

# if folder exists, delete to start clean
if os.path.exists(pathName):
    try:
        shutil.rmtree(pathName)
    except OSError as e:
        print("Error: %s : %s" % (pathName, e.strerror))

# create again
os.mkdir(pathName)

# Shouldn't need to change anything below this line
#####################


########################################################################################################################
def calc_Rwrap(device):
    return 0.95 * device.ysize / 2


########################################################################################################################
def create_sin_txt(text, labelsFontSize, expd=0.25):
    txt = pg.text(text, size=labelsFontSize, font=fontName, justify='center', layer=TEXT)
    w = txt.xsize
    h = txt.ysize
    margin = np.min(np.array([w, h])*expd)

    fillExclude = pg.rectangle(size=(w + margin, h + margin), layer=ALLB)
    fillExclude.center = txt.center
    txt.add(fillExclude)

    txt.add(pg.bbox(fillExclude.bbox, layer=EXCPT))
    return txt


########################################################################################################################
def transitionS(x_0, y_0, x_1, y_1, t0=0.0, k0=0.0, t1=0.0, k1=0.0, Dmax=0, dmax=0, pts_per_um=2):
    clothoid_list = pyclothoids.SolveG2(x_0, y_0, np.deg2rad(t0), k0, x_1, y_1, np.deg2rad(t1), k1, Dmax=Dmax, dmax=dmax)
    xs = np.array([])
    ys = np.array([])
    for i, clth in enumerate(clothoid_list):
        n_pts = 1 + pts_per_um * np.round(np.max([5, clth.Parameters[-1]]))
        pts = clth.SampleXY(int(n_pts))
        if i == 0:
            idx_start = 0
        else:
            idx_start = 1
        xs = np.append(xs, pts[0][idx_start::])
        ys = np.append(ys, pts[1][idx_start::])

    points = np.array((xs, ys)).T

    return points


########################################################################################################################
def ligenTaperCoupler(width, orient=0):
    D = Device(name='AN350BB_EdgeCoupler_Lensed_C')

    BBsize = (430, 30)
    D << pg.rectangle(size=BBsize, layer=CELLSIZE) # global footprint

    wgplchldr = D << pg.rectangle(size=(5, width), layer=(2, 2)) # waveguide placeholder
    wgplchldr.xmin = D.xmin

    fbp = D << pg.rectangle(size=(10, BBsize[1]), layer=(101, 2)) # Fiber port
    fbp.xmax = D.xmax

    lbl = D << pg.text(D.name.upper(), size=2, layer=CELLNAME)  # Add labeling
    lbl.center = D.center

    D.align(elements='all', alignment='y')  # centering

    # Add connector
    D.add_port(name='invTaperIn', midpoint=(wgplchldr.xmax, wgplchldr.y), width=width, orientation=180)

    D.rotate(orient)
    D.move((wgplchldr.xmax, wgplchldr.y), (0, 0))
    D.flatten()

    return D


########################################################################################################################
def waveguide(width=10, height=1, layer=None):
    WG = Device('waveguide')
    WG.add_polygon([(0, 0), (width, 0), (width, height),
                    (0, height)], layer=layer)
    WG.add_port(name='in', midpoint=[
                0, height / 2], width=height, orientation=180)
    WG.add_port(name='out', midpoint=[
                width, height / 2], width=height, orientation=0)
    return WG


########################################################################################################################
def expandGeometry(element, offset):
    # Enlarge the geometry if needed
    if offset != 0:
        print('Expanding the GDS geometry ...')
        L = pg.extract(element, layers=[0])
        element = element.remove_layers(layers=[0])
        L = pg.offset(L, distance=offset, precision=1e-6, layer=0)
        element << L
    return element


########################################################################################################################
def createRing(paramsTable):
    ##############################
    # Create the resonator + coupling section
    ##############################

    # _________________________________
    # Load a GDS Ring
    if paramsTable['Type'] == 'gdsRing':
        fname = paramsTable['gdsFile']
        print("Dev %i: gds Ring, loading %s" % (paramsTable['Dev'], fname))
        res = pg.import_gds(filename='./ExternalGDS/' + fname + '.gds', flatten=True)
        res.center = (0, 0)
        # Declare the geometrical parameters ! [still asumes a circle geometry afterward especially for pulley]
        RR = (res.ymax - res.ymin) / 2
        RW = 0

        res = expandGeometry(res, paramsTable['offset'])
        res.name = 'resonator'

    else: # A ring defined by parameters
        RW = paramsTable['RW'] + 2*paramsTable['offset']
        RR = paramsTable['RR']
        APhC = paramsTable['APhC']
        # _________________________________
        # NO photonic crystal, simple ring
        if APhC == 0:
            print("Dev %i: Simple ring" % paramsTable['Dev'])
            res = pg.ring(radius=RR, width=RW, angle_resolution=0.2, layer=SiNMAIN) #angle_resolution=360/(4*np.pi*RR)
            res.name = 'ring'
            if len(res.get_polygons()[0]) > 4600:
                res.polygons[0].fracture(max_points=4500, precision=0.001)

        # _________________________________
        # photonic crystal ring
        else:
            mPeriodPump = int(np.round(paramsTable['m']))
            if mPeriodPump % 2 != 0:
                warnings.warn('mPeriodPump is ODD (=%i)' % mPeriodPump)

            # _________________________________
            # Check if it's a multiPhC
            if "PhCProfile" in paramsTable.keys() and isinstance(paramsTable['PhCProfile'],  str):
                print("Dev %i: Multi-PhC ring" % paramsTable['Dev'])
                # read and copy (in backup) the profile file and deals w/ complex numbers
                PhCFileRoot = './DesignsTables/MultiPhCProfiles/'
                PhCFile = paramsTable['PhCProfile'] + '.txt'
                shutil.copyfile(PhCFileRoot + PhCFile,
                                os.path.join(pathName, PhCFile))
                PhCProfile = pd.read_csv(
                    PhCFileRoot + PhCFile,
                    converters={'APhCNorm': lambda s: complex(s.replace('i', 'j'))}
                )
                mPhC = 2 * np.array(PhCProfile['mu']) + mPeriodPump
                APhC = APhC * np.array(PhCProfile['APhCNorm'])
                PhCPhase = np.array(PhCProfile['phase'])
                phi = np.linspace(-np.pi, np.pi, overSamplingFactPhC * np.max(mPhC) + 1)
                phi = phi[:-1]
                PhC_profile = np.real( 0.5 * np.sum( APhC * np.cos(np.outer(mPhC, phi).T + mPhC*PhCPhase), axis=1) )

                #plt.plot(phi, PhC_profile)

                # find zero xssing
                zxc_idx = np.nonzero(PhC_profile * np.roll(PhC_profile, 1) <= 0)[0]
                zxc_pos = PhC_profile[zxc_idx] > 0
                PhC_profile = np.roll(PhC_profile, -zxc_idx[zxc_pos][0])
                zxc_idx = np.sort((zxc_idx - zxc_idx[zxc_pos][0]) % len(PhC_profile))

                PhC_profile_original = np.copy(PhC_profile) # Backup for comparison
                phi_original = np.copy(phi)

                # Thresholding operation for LGT DRC
                amp_thres =  0.25 / 2.0

                analytic_signal = signal.hilbert(PhC_profile)
                PhC_profile_posEnvelope = np.abs(analytic_signal)
                PhC_profile_phase = np.angle(analytic_signal)

                # Implement the gradual 'squarification' of the profile
                #PhC_profile[sel_amp_over] = np.sign(PhC_profile[sel_amp_over]) * PhC_profile_posEnvelope[sel_amp_over] * 0.76
                #PhC_profile[sel_amp_over] = np.tanh(2*PhC_profile[sel_amp_over]/amp_thres) * 0.76 * PhC_profile_posEnvelope[sel_amp_over]

                rescaleFactor_square = 0.72 # In theory should be 1/(np.sqrt(2)) = 0.7071 but I likely need to account for etching issues
                geom_saturationFactor = np.minimum(0.99, PhC_profile_posEnvelope / amp_thres)
                squareness = 1/(1 - geom_saturationFactor) - 1 # How quickly do we go toward a square?
                rms = (1/np.sqrt(2)-1) /(1+0.2262 * squareness**1.911) + 1 # fit of RMS value of a squarish waveform
                local_amplitude_rescale = 1/(np.sqrt(2) * rms)
                local_amplitude_rescale = np.maximum(rescaleFactor_square, local_amplitude_rescale * 1.01) # WARNING the 1% correction is empirical
                #local_amplitude_rescale = 1 - (1-rescaleFactor) * np.tanh(saturationFactor * PhC_profile_posEnvelope / (2* amp_thres))
                PhC_profile = (np.tanh(squareness * np.cos(PhC_profile_phase)) / np.tanh(squareness)
                               * local_amplitude_rescale * PhC_profile_posEnvelope)

                #plt.plot(phi, PhC_profile_original, '.-')
                #plt.plot(phi, PhC_profile, '.-')

                # Binarize if squareness is sufficient
                above_mask = squareness > 10  # amp thres detect
                above_mask = minimum_filter(maximum_filter(above_mask.astype(int), overSamplingFactPhC + 2, mode='wrap'),overSamplingFactPhC + 2, mode='wrap')
                above_mask = above_mask.astype(bool)
                segments = []
                current_state = above_mask[0]
                seg_start = 0
                for i in range(1, len(phi)):
                    if above_mask[i] != current_state:
                        segments.append([seg_start, i - 1, current_state])
                        seg_start = i
                        current_state = above_mask[i]
                # Append the last segment
                segments.append([seg_start, len(phi) - 1, current_state])

                n_neighbor = int(overSamplingFactPhC/2) - 2
                #PhC_profile_posEnvelope = maximum_filter(median_filter(abs(PhC_profile), 5, mode='wrap'), int(overSamplingFactPhC / 2) + 2, mode='wrap')
                #PhC_profile_posEnvelope = uniform_filter(maximum_filter(PhC_profile_posEnvelope, round(overSamplingFactPhC / 2) + n_neighbor,
                #                                         mode='wrap'), size=n_neighbor, mode='wrap')
                #plt.cla()
                #plt.plot(PhC_profile)
                #plt.plot(zxc_idx, np.zeros_like(zxc_idx), '.')
                #plt.plot([x[0] for x in segments], np.zeros(len(segments)), 'bx')
                #plt.plot([x[1] for x in segments], np.zeros(len(segments)), 'rx')

                for idx_seg, segment in enumerate(segments):
                    # Find the closest smaller zxc_idx to each start and the closest larger zxc_idx to each end
                    if idx_seg > 0:
                        closest_starts = np.searchsorted(zxc_idx, segment[0], side='left')
                        closest_starts = np.clip(closest_starts, 0, len(zxc_idx) - 1)
                        segment[0] = zxc_idx[closest_starts]
                    if idx_seg < len(segments)-1:
                        closest_ends = np.searchsorted(zxc_idx, segment[1], side='right')
                        closest_ends = np.clip(closest_ends, 0, len(zxc_idx) - 1)
                        segment[1] = zxc_idx[closest_ends]
                    if segment[2]: # means: is above, then 'threshold the values (full binarization)
                        seg_range = np.arange(segment[0]-1, segment[1]+1)
                        PhC_profile[seg_range] = rescaleFactor_square * PhC_profile_posEnvelope[seg_range] * np.sign(PhC_profile[seg_range])

                #PhC_profile = median_filter(PhC_profile, 5, mode='wrap')
                #plt.plot([x[0] for x in segments], np.zeros(len(segments)), 'bs')
                #plt.plot([x[1] for x in segments], np.zeros(len(segments)), 'rs')

                # if paramsTable['Dev'] == 79:
                #    print('BREAK')

                d_phi = 0.17/mPeriodPump

                # Compose a new PhC vector that separates the part that are binarized and the rest
                segments = [tuple(x) for x in segments]
                phi_final_list = []
                phc_final_list = []

                for idx_seg, (start, end, is_above) in enumerate(segments):
                    if not is_above:
                        seg_range = np.arange(start, end)
                        #seg_sel = np.concatenate((np.abs(np.diff(PhC_profile[seg_range])) > 0, [True]))
                        #seg_range = np.unique(np.concatenate(([start], seg_range[seg_sel], [end])))
                        # BELOW THRESHOLD: keep point
                        phi_final_list.append(phi[seg_range])
                        phc_final_list.append(PhC_profile[seg_range])
                    else:
                        #zxc_segment = np.unique( np.concatenate(([start], zxc_idx[(zxc_idx >= start) & (zxc_idx <= end)], [end])) )
                        zxc_segment = zxc_idx[(zxc_idx >= start) & (zxc_idx <= end+1)]

                        zxc_segment_shift = (zxc_segment-1) % len(PhC_profile)
                        PhC_profile_segment = np.vstack((PhC_profile[zxc_segment_shift], PhC_profile[zxc_segment]))
                        PhC_profile_segment = PhC_profile_segment.T.flatten()

                        phi_segment = np.vstack((phi[zxc_segment_shift], phi[zxc_segment]))
                        phi_segment = np.exp(1j * phi_segment)
                        phi_segment = np.tile(np.mean(phi_segment, axis=0), (2, 1))
                        phi_segment = np.angle(phi_segment)
                        phi_segment = phi_segment.T.flatten()

                        idx_seg_start = 0
                        if idx_seg > 0:
                            #phi_segment[0] += -d_phi # corresponds to approx squareness of 10
                            phi_segment[1] += +d_phi

                        if idx_seg < len(segments)-1:
                            phi_segment[-2] += -d_phi
                            #phi_segment[-1] += +d_phi

                        phi_final_list.append(phi_segment)
                        phc_final_list.append(PhC_profile_segment)

                for idx_seg, (start, end, is_above) in enumerate(segments):
                    idx_prev_seg = (idx_seg - 1) % len(segments)
                    idx_next_seg = (idx_seg + 1) % len(segments)
                    if segments[idx_next_seg][2] and not is_above:
                        phi_final_list[idx_seg] = phi_final_list[idx_seg][:-n_neighbor]
                        phc_final_list[idx_seg] = phc_final_list[idx_seg][:-n_neighbor]
                    if segments[idx_prev_seg][2] and not is_above:
                        phi_final_list[idx_seg] = phi_final_list[idx_seg][n_neighbor:]
                        phc_final_list[idx_seg] = phc_final_list[idx_seg][n_neighbor:]

                phi = np.concatenate(phi_final_list)
                PhC_profile = np.concatenate(phc_final_list)
                if segments[0][2] and segments[-1][2]:
                    PhC_profile = np.roll(PhC_profile, -1)
                    phi = np.roll(phi, -1)

                # SANITY CHECKS
                # if segments[idx_seg][2]:
                #     plt.plot(phi, PhC_profile)
                #     plt.show()
                #     plt.plot((1 + PhC_profile) * np.cos(phi), (1 + PhC_profile) * np.sin(phi))
                #     plt.show()
                # nXssing = np.sum(PhC_profile * np.roll(PhC_profile, 1) <= 0)
                # if nXssing != 2*(mPeriodPump+2):
                #     u = (1+PhC_profile_original)
                #     plt.plot(u * np.cos(phi_original), u * np.sin(phi_original))
                #     u = (1 + PhC_profile)
                #     plt.plot(u * np.cos(phi), u * np.sin(phi))
                #     plt.plot(np.cos(phi_original[zxc_idx]), np.sin(phi_original[zxc_idx]), 'o')
                #     plt.show()

                #     zxc_idx = PhC_profile * np.roll(PhC_profile, 1) <= 0
                #     zxc_idx[-1] = False
                #     phi_zc = np.vstack((phi[np.roll(zxc_idx, -1)], phi[zxc_idx]))
                #     phi_zc = np.exp(1j * phi_zc)
                #     phi_zc = np.mean(phi_zc, axis=0)
                #     phi_zc = np.angle(phi_zc)
                #     phi_zc = phi_zc.flatten()
                #     envlp = maximum_filter(np.abs(PhC_profile), overSamplingFactPhC, mode='wrap')
                #     pbidx = np.append((RR-RW/2)*np.diff(phi_zc) < 0.2, False)
                #     if np.any(pbidx):
                #         zxc_idx = np.argwhere(zxc_idx)
                #         plt.plot(phi, PhC_profile)
                #         plt.plot(phi_zc[pbidx], PhC_profile[zxc_idx[pbidx]], 'ro')
                #         plt.plot(phi_zc[np.roll(pbidx, 1)], PhC_profile[zxc_idx[np.roll(pbidx, 1)]], 'ro')
                #         plt.show()
                #         raise ValueError("Wrong number of zero xssing, expect %i, measured %i"%(2*mPeriodPump, nXssing))

                # plot it for debugging purpose
                if args.exportProfiles:
                    fig = plt.figure()
                    plt.plot(phi, PhC_profile)
                    fig.suptitle("Dev %i: Multi-PhC ring, %s" % (paramsTable['Dev'], paramsTable['PhCProfile']))
                    fig.savefig(os.path.join(pathName, 'Dev' + str(paramsTable['Dev']) + '_profile.png'), bbox_inches='tight')
                    plt.close(fig)

            # _________________________________
            # Simple PhC
            else:
                print("Dev %i: Single PhC ring" % paramsTable['Dev'])

                if paramsTable['PhCSquareness'] > 10:
                    PhC_profile = APhC/2 * np.tile([1, -1, -1, 1], (1,mPeriodPump)).flatten()
                    phi = np.linspace(-np.pi, np.pi, 2*mPeriodPump+1)
                    phi = phi[:-1]
                    phi = np.tile(phi, (2,1)).T.flatten()

                else:
                    if paramsTable['PhCSquareness'] != 0:
                        modfun = lambda x: np.tanh(paramsTable['PhCSquareness'] * np.cos(x)) / np.tanh(
                            paramsTable['PhCSquareness'])
                    else:
                        modfun = lambda x: np.cos(x)
                    phi = np.linspace(-np.pi, np.pi, overSamplingFactPhC * mPeriodPump + 1)
                    phi = phi[0:-1]
                    PhC_profile = 0.5 * APhC * modfun(mPeriodPump * phi)

            #a_max = np.max(PhC_profile)

            # Now cast the PhC into inner and outer points
            polar_arg = np.exp(1j * phi)

            n_pts_tot = len(PhC_profile)
            res = Device()

            n_pts_per_cut = 1500
            cutpos = np.arange(n_pts_per_cut, n_pts_tot, n_pts_per_cut)
            pos_phc_idx = np.argwhere(PhC_profile > 0)
            for ii in range(len(cutpos)):
                nearestIdb = np.argmin(np.abs(pos_phc_idx - cutpos[ii]))
                cutpos[ii] = pos_phc_idx[nearestIdb][0]

            if n_pts_tot - cutpos[-1] < n_pts_per_cut/4:
                cutpos = cutpos[:-1]

            PhC_profile = np.array_split(PhC_profile, cutpos)
            polar_arg = np.array_split(polar_arg, cutpos)

            for i_seg, pol_segment in enumerate(polar_arg):
                phc_segment = PhC_profile[i_seg]
                if i_seg == len(polar_arg) - 1:
                    pol_segment = np.append(pol_segment, polar_arg[0][0])
                    phc_segment = np.append(phc_segment, PhC_profile[0][0])
                else:
                    pol_segment = np.append(pol_segment, polar_arg[(i_seg + 1)][0])
                    phc_segment = np.append(phc_segment, PhC_profile[(i_seg + 1)][0])
                    
                pts_in = (RR - 0.5 * RW + phc_segment) * pol_segment
                phi_seg = np.unwrap(np.angle(pol_segment[[0,-1]]))
                outerPt = np.maximum(3, round(1e3/np.pi * (np.max(phi_seg) - np.min(phi_seg))))
                pol_outer = np.exp(1j * np.linspace(phi_seg[0], phi_seg[1], outerPt))
                #pol_outer = np.exp(1j * np.linspace(-np.pi, np.pi, 2000))
                pts_out = (RR + 0.5 * RW) * pol_outer
                pts = np.array(np.concatenate((pts_in, np.flip(pts_out)))).T
                pts = (np.real(pts), np.imag(pts))
                res.add_polygon(pts, layer=SiNMAIN)

            # quickplot(res)
            # plt.plot((RR - RW/2 + PhC_profile_original) * np.cos(phi_original), (RR - RW/2 + PhC_profile_original) * np.sin(phi_original))
            # plt.show()

            res.flatten()

            #res.add( pg.ring(radius=RR - a_max/2, width=(RW + a_max)*1.2, angle_resolution=0.5, layer=EXCPT) ) # Add an exclusion zone in the phC

            res.name = 'PhCR'

    return res


########################################################################################################################
def custom_spiral(num_turns=5, out_angle=90, gap=1, inner_gap=2, num_pts=10000):
    # Establishing number of turns in each arm
    if num_turns <= 1:
        raise ValueError("num_turns must be greater than 1")
    out_angle = np.deg2rad(out_angle)

    # Establishing relevant angles and spiral/centre arc parameters
    a1 = np.pi / 2
    a2 = np.array([np.pi * num_turns + a1, np.pi * num_turns + a1 + out_angle])
    a = inner_gap / 2 - gap / 2
    b = gap / np.pi
    Rc = inner_gap * np.sqrt(1 + (b / (a + b * a1)) ** 2) / 4
    theta = np.degrees(2 * np.arcsin(inner_gap / 4 / Rc))

    # Establishing number of points in each arm
    #s_centre = Rc * np.radians(theta)
    #s_spiral = ((a + a2 * b) ** 2 + b**2) ** (3 / 2) / (3 * (a * b + (a2 * b**2))) # Spiral length ??
    #z = num_pts / (s_spiral[0] + s_spiral[1] + 2 * s_centre)
    #num_pts0 = int(z * s_centre)
    #num_pts1 = int(z * s_spiral[0])
    #num_pts2 = int(z * s_spiral[1]) - num_pts1

    # Forming both spiral arms
    num_pts1 = round(np.abs(a1-a2[0])/np.pi * 900)
    arm1 = np.linspace(a1, a2[0], num_pts1)
    arm2 = np.linspace(a2[0], a2[1], round(np.abs(a2[0]-a2[1])/np.pi * 900))[1:]
    a_spiral = [arm1, np.concatenate([arm1, arm2])]
    r_spiral = [a + b * a_spiral[0], a + b * a_spiral[1]]
    x_spiral = [np.zeros(num_pts1), np.zeros(len(a_spiral[1]))] # Initialize
    y_spiral = [np.zeros(num_pts1), np.zeros(len(a_spiral[1]))]
    for i in range(2):
        x_spiral[i] = r_spiral[i] * np.cos(a_spiral[i])
        y_spiral[i] = r_spiral[i] * np.sin(a_spiral[i])

    x_spiral[1] = -np.flip(x_spiral[1])
    y_spiral[1] = -np.flip(y_spiral[1])
    r_spiral[1] = -np.flip(r_spiral[1])
    a_spiral[1] = np.flip(a_spiral[1])
    start_angle = np.arctan2(y_spiral[1][-1] - y_spiral[1][-2], x_spiral[1][-1] - x_spiral[1][-2]) / np.pi * 180

    # Forming centre arcs
    # pts = _rotate_points(arc(Rc, theta, 360 * num_pts0 / theta).points, -theta / 2 + 90)
    d_max_param = 0
    Dmax_param = 0
    pts1 = transitionS(x_spiral[1][-1], y_spiral[1][-1],
                       0, 0,
                       t0=start_angle, t1=0,
                       k0=1/r_spiral[1][-1], k1=0,
                       dmax=d_max_param, Dmax=Dmax_param)
    pts2 = transitionS(0, 0,
                       x_spiral[0][0], y_spiral[0][0],
                       t0=0, t1=start_angle,
                       k0=0, k1=1/r_spiral[0][0],
                       dmax=d_max_param, Dmax=Dmax_param)

    pts = np.concatenate([pts1[:-1], pts2])
    x_centre = pts[:, 0]
    y_centre = pts[:, 1]

    # Combining into final spiral
    x = np.concatenate([x_spiral[1][:-1], x_centre[:-1], x_spiral[0]]) # reject first and last points of centre
    y = np.concatenate([y_spiral[1][:-1], y_centre[:-1], y_spiral[0]])
    points = np.array((x, y)).T

    P = Path()
    # Manually add points & adjust start and end angles
    P.points = points
    nx1, ny1 = points[1] - points[0]
    P.start_angle = np.arctan2(ny1, nx1) / np.pi * 180 # Rounding helps to get thing connected
    nx2, ny2 = points[-1] - points[-2]
    P.end_angle = np.arctan2(ny2, nx2) / np.pi * 180

    return P, r_spiral

########################################################################################################################
def create_element(paramsTable):
    ##############################
    # Check device type and load / create
    ##############################
    # _________________________________
    # Ring
    if paramsTable['Type'] in ['ring', 'gdsRing']:
        element = createRing(paramsTable)
        
    # _________________________________
    # Racetrack resonator with a straight section and adiabatic bends
    elif paramsTable['Type'] == 'racetrack':
        print("Dev %i: Racetrack resonator" % paramsTable['Dev'])

        p = 0.5 # Racetrack euler p value (hardcoded)

        RR = paramsTable['RR']
        if type(RR) == str:
            RR = eval(RR)
            R_bend = RR[1]
            RR = RR[0]
            dx = np.max([1, np.pi * (RR - (p + 1) * R_bend)])
            print('Min. Radius was imposed to %g um' % R_bend)
        else:
        # element = pg.racetrack_gradual(width=paramsTable['RW'], R=5, N=3, layer=SiNMAIN)
            dx = np.max([1, np.min([np.pi * (RR - (p + 1)*R_min), LxChip/4])])
            R_bend = (np.pi * RR - dx) / (np.pi * (p + 1))  # See publication on Euler bends
            print('Min. Radius %g um' % R_bend)

        P = Path()
        P.append([
            pp.straight(length=dx, num_pts=3),  # Should have a curvature of 0
            pp.euler(radius=R_bend, p=p, angle=180, use_eff=False, num_pts=1800),  # p=0.5, use_eff=False
            pp.straight(length=dx, num_pts=3),  # Should have a curvature of 0
            pp.euler(radius=R_bend, p=p, angle=180, use_eff=False, num_pts=1800)  # Should have a curvature of 1/10
        ])
        fracLengthErr = (P.length()) / (2 * np.pi * RR) - 1
        print("Fractional difference from racetrack pathlength to RR perimeter {0:.3%}".format(fracLengthErr * 100))

        X = CrossSection()
        # Add a single "section" to the cross-section
        X.add(width=paramsTable['RW'], offset=0, layer=SiNMAIN, ports=(1, 2), name='raceTrack')

        element = P.extrude(X)
        element.name = 'racetrack'
        element.ymax = paramsTable['RW']/2
        element.x = 0
        element = element.flatten()
        element.polygons[0].fracture(max_points=4500, precision=0.001)

    # _________________________________
    # Square-ich resonator with a straight sections and adiabatic 90 bends
    elif paramsTable['Type'] == 'square':
        print("Dev %i: Square-ish resonator" % paramsTable['Dev'])

        p = 0.3  # Racetrack euler p value (hardcoded)

        RR = paramsTable['RR']
        if type(RR) == str:
            RR = eval(RR)
            dy = RR[1]
            RR = RR[0]
            perim = 2 * np.pi * RR
            bend_L = np.pi/2 *  (p + 1) * R_min
            dx = 1/2  * ( perim - 4 * bend_L - 2*dy )
        else:
            # element = pg.racetrack_gradual(width=paramsTable['RW'], R=5, N=3, layer=SiNMAIN)
            dx = np.max([1, np.min([np.pi/2 * (RR - (p + 1) * R_min), LxChip / 4])])
            dy = dx
            print('Min. Radius %g um' % R_bend)

        R_bend = R_min
        P = Path()
        P.append([
            pp.straight(length=dx, num_pts=3),  # Should have a curvature of 0
            pp.euler(radius=R_bend, p=p, angle=90, use_eff=False, num_pts=1800),  # p=0.5, use_eff=False
            pp.straight(length=dy, num_pts=3),  # Should have a curvature of 0
            pp.euler(radius=R_bend, p=p, angle=90, use_eff=False, num_pts=1800),
            pp.straight(length=dx, num_pts=3),
            pp.euler(radius=R_bend, p=p, angle=90, use_eff=False, num_pts=1800),
            pp.straight(length=dy, num_pts=3),
            pp.euler(radius=R_bend, p=p, angle=90, use_eff=False, num_pts=1800)
        ])
        fracLengthErr = (P.length()) / (2 * np.pi * RR) - 1
        print("Path length: {0:.3}, RR perim {1:.3}, Fractional difference from square pathlength to RR perimeter {2:.3%}".format(P.length(), 2 * np.pi * RR, fracLengthErr * 100))

        X = CrossSection()
        # Add a single "section" to the cross-section
        X.add(width=paramsTable['RW'], offset=0, layer=SiNMAIN, ports=(1, 2), name='raceTrack')

        element = P.extrude(X)
        element.name = 'square'
        element.ymax = paramsTable['RW'] / 2
        element.x = 0
        element = element.flatten()
        element.polygons[0].fracture(max_points=4500, precision=0.001)
        
    # _________________________________
    # Snail resonator with a spiral section and adiabatic bends
    elif paramsTable['Type'] == 'snail':
        RR = paramsTable['RR']
        LR = 2*np.pi * RR
        R_bend = 2*R_min # smallest radius in the center
        gap = paramsTable['RW'] + 4
        N_turns = 0.5 * (np.sqrt(R_min**2 + gap/2*LR/np.pi) - R_min)/(gap/2)
        delta_path = LR
        straight_corr = 0
        n_iter = 0
        while np.abs(delta_path)/LR > 1e-6:
            spiral, r_spiral = custom_spiral(num_turns=N_turns, gap=gap, inner_gap=2*R_bend, num_pts=int(np.round(LR)))
            R_spiral_max = r_spiral[0][-1]
            R_spiral_min = r_spiral[0][0]
            spiral_transition_in = transitionS(
                0, 0, R_spiral_max, -R_spiral_max - gap,
                t0=0, t1=-90,
                k0=0, k1=-1 / R_spiral_max
            )

            theta_out = np.arccos((R_spiral_max - R_min)/(R_spiral_max + R_min + gap))
            angle_out = np.rad2deg(theta_out)
            straight_dist = (R_spiral_max + R_min) * np.sin(theta_out) - R_min/2 + gap/2 + straight_corr

            loop_transition = np.flipud(transitionS(
                R_min, -R_min/2,
                R_min*np.cos(theta_out), R_min*np.sin(theta_out),
                t0=90, t1=90+angle_out,
                k0=0, k1=1/R_min
            ))
            loop = pp.arc(radius=R_min, angle=-180, num_pts=1800)

            P = Path()
            P.append([
                loop,
                loop_transition
            ])
            rotate_angle = P.end_angle
            P.append([
                pp.straight(straight_dist),
                spiral_transition_in,
                spiral
            ])
            P.rotate(-rotate_angle)
            P.center = (0,0)
            P.ymax = 0

            points_list = P.points
            transition_out = transitionS(
                points_list[-1, 0], points_list[-1, 1],
                points_list[0, 0], points_list[0, 1],
                t0=180, t1=angle_out - 180,
                k0=1 / R_spiral_max, k1=-1 / R_min,
                dmax=0, Dmax=100
            )

            points_list = np.concatenate([points_list[0:-1, :], transition_out])
            P = Path(points_list)
            P.start_angle = -(180-angle_out)
            P.end_angle = -(180 - angle_out)

            L_path = P.length()
            delta_path = LR - L_path
            n_iter += 1
            delta_N_turns = delta_path/np.pi * 1/(2*R_spiral_max) * 1/(1+0.05*n_iter)
            print(delta_N_turns)
            if np.abs(delta_N_turns) > 1e-3:
                N_turns += delta_N_turns
            else:
                straight_corr += delta_path/10
            print('Iter %i, Snail resonator Path length %g (%g turns) and %g designed, difference is %g' % (n_iter, L_path, N_turns, LR, delta_path))

        # Now let's deal with the PhC case
        APhC = paramsTable['APhC']
        m0_PhC = np.round(paramsTable['m'])

        N_per_seg = 2000 # Number of points in each segment for subdivided path

        if not(APhC == 0 or np.isnan(m0_PhC)):

            # _________________________________
            # Check if it's a multiPhC
            if "PhCProfile" in paramsTable.keys() and isinstance(paramsTable['PhCProfile'], str):
                print("Dev %i: Multi-PhC snail" % paramsTable['Dev'])
                # read and copy (in backup) the profile file and deals w/ complex numbers
                PhCFileRoot = './DesignsTables/MultiPhCProfiles/'
                PhCFile = paramsTable['PhCProfile'] + '.txt'
                shutil.copyfile(PhCFileRoot + PhCFile,
                                os.path.join(pathName, PhCFile))
                PhCProfile = pd.read_csv(PhCFileRoot + PhCFile)
                mPhC = 2 * np.array(PhCProfile['mu']) + m0_PhC
                APhC_norm_array = np.array(PhCProfile['APhCNorm'])
                PhCPhase = np.array(PhCProfile['phase'])
                n_interp = overSamplingFactPhC * np.max(mPhC) + 1
                def wgModFun(t):
                    PhC_profile = paramsTable['RW']/2 + 0.5 * APhC * np.sum( APhC_norm_array * np.cos(2*np.pi * np.outer(mPhC, t).T + mPhC*PhCPhase), axis=1)
                    return PhC_profile

            # _________________________________
            # Simple PhC
            else:
                print("Dev %i: Single PhC snail" % paramsTable['Dev'])
                n_interp = overSamplingFactPhC * m0_PhC + 1
                def wgModFun(t):
                    PhC_profile = paramsTable['RW']/2 + 0.5 * APhC * np.cos(2*np.pi * m0_PhC * t)
                    return PhC_profile

            # We need to interpolate the points to have a sufficient resolution
            print('Detected PhC on spiral resonator, interpolating for appropriate resolution ...')

            points_list = P.points
            s, K = P.curvature()
            s = np.insert(s, 0, 0)
            interpolator = CubicSpline(s, points_list, axis=0)
            s_interp = np.linspace(0, s[-1], int(n_interp)) # Interpolate the path to have sufficient resolution for the PhC
            points_list = interpolator(s_interp)

            P = Path(points_list)
            P.start_angle = -(180 - angle_out)
            P.end_angle = -(180 - angle_out)

            P_outer = P.copy()
            P_outer.offset(offset=-paramsTable['RW'] / 2)

            P.offset(offset=wgModFun)

            points_idx = range(len(points_list))
            points_idx = np.array_split(points_idx, len(points_idx) // N_per_seg + (len(points_idx) % N_per_seg != 0))

            element = Device()
            for seg_idx, segment_idx in enumerate(points_idx):
                if seg_idx < len(points_idx) - 1:
                    segment_idx = np.append(segment_idx, points_idx[(seg_idx + 1)][0])
                outer_idx = np.rint(np.linspace(segment_idx[0], segment_idx[-1], round(N_per_seg/overSamplingFactPhC))).astype(int)
                element.add_polygon(np.array(np.concatenate((P.points[segment_idx, :], np.flip(P_outer.points[outer_idx, :], axis=0)))).T, layer=SiNMAIN)

        else:
            points_list = P.points
            points_list = np.array_split(points_list, len(points_list) // N_per_seg + (len(points_list) % N_per_seg != 0))

            element = Device()
            for seg_idx, segment_points in enumerate(points_list):
                if seg_idx < len(points_list) - 1:
                    segment_points = np.vstack((segment_points, points_list[(seg_idx + 1)][0, :]))

                P = Path(segment_points)
                if seg_idx == 0:
                    P.start_angle = -(180 - angle_out)
                else:
                    P.start_angle = outAngle
                if seg_idx == len(points_list) - 1:
                    P.end_angle = -(180 - angle_out)
                outAngle = P.end_angle

                X = CrossSection()
                X.add(width=paramsTable['RW'], offset=0, layer=SiNMAIN, ports=(1, 2), name='raceTrack')
                element.add(P.extrude(X))



        element.mirror((1, 0))
        element.movex(straight_dist/4)
        element.ymax = paramsTable['RW']/2
        element = element.flatten()
        #element.polygons[0].fracture(max_points=10000, precision=0.0005)
        element.name = 'spiral'
        #quickplot(element)

    # _________________________________
    # GDS file with ports defined in a yaml file
    elif paramsTable['Type'] == 'gdsElement':
        print("Dev %i: gds structure loading %s" % (paramsTable['Dev'], paramsTable['gdsFile']))
        element = pg.import_gds(filename='./ExternalGDS/' + paramsTable['gdsFile'] + '.gds', flatten=True)
        element = expandGeometry(element, paramsTable['offset'])

    # _________________________________
    # Simple Waveguide
    elif paramsTable['Type'] == 'waveguide':
        print("Dev %i: Waveguide" % paramsTable['Dev'])
        element = pg.straight(size=(paramsTable['WGW'] + 2 * paramsTable['offset'], paramsTable['Lc']), layer=0)
        element.rotate(90, center=(0, 0))
        element.center = (0, 0)
        element.ports[1].name = 'in'  # rename the ports as in / out
        element.ports[2].name = 'out'

    # _________________________________
    # Undefined type
    else:
        raise NameError('Unknown Type %s in paramsTable' % str(paramsTable['Type']))

    return element


########################################################################################################################
def addRingCoupler(resonator_assembly, paramsTable):

    ##############################
    # Create the coupler section (pulley or not ...) and add connectors for later WG routing
    ##############################

    wg_w = paramsTable['WGW'] + 2 * paramsTable['offset']
    gap = paramsTable['gc']

    X_wg = CrossSection()
    X_wg.add(width=wg_w, offset=0, layer=SiNMAIN, name='busCoupler', ports=['in', 'out'])

    # Create the path here
    if paramsTable['Type'] == 'ring':
        P, rotate_angle = ringCouplerPath(paramsTable)
        P.ymin = -paramsTable['RR'] - paramsTable['RW']/2 - gap - wg_w / 2 # Move the bus / pulley in the right position

    elif paramsTable['Type'] in ['racetrack', 'snail', 'square']:
        P = raceTrackCouplerPath(paramsTable)
        P.x = 0
        P.ymax = resonator_assembly.ymin - gap - wg_w / 2 # Move the coupler in the right position

    # Give the path a width to define a waveguide
    pulley_wg = P.extrude(X_wg)
    pulley_wg = resonator_assembly << pulley_wg

    if paramsTable['Type'] == 'ring' and rotate_angle != 0:
        resonator_assembly.rotate(rotate_angle)
        pulley_wg.ports['in'].orientation=0

    # move to have the WG connectors to zero (easier coordinates)
    resonator_assembly.movey(-pulley_wg.ports['in'].y + pulley_wg.ports['in'].width/2)

    # Add the ports to the main device and absorb the coupler
    for prt in pulley_wg.ports.values():
        resonator_assembly.add_port(port=prt)
    resonator_assembly.absorb(pulley_wg)

    return resonator_assembly

########################################################################################################################
def ringCouplerPath(paramsTable):
    Lc = paramsTable['Lc']
    RW = paramsTable['RW'] + 2 * paramsTable['offset']
    RR = paramsTable['RR']
    gap = paramsTable['gc']
    RPulley = RR + 0.5 * RW + gap + 0.5 * paramsTable['WGW'] - 2 * paramsTable['offset']  # center WG line

    rotate_angle = 0

    if np.isnan(RPulley):
        raise NameError('Invalid number in the coupling distance value')

    P = Path()

    if Lc > 1e-6:  # Pulley mode active
        pulleyAngle_rad = Lc / RPulley
        pulleyAngle = np.rad2deg(pulleyAngle_rad)
        #p_euler = 0.4
        p_euler = 0.8 * 1 / np.sqrt(1 + Lc / d_safe)
        eulerAngle = np.round(pulleyAngle/(1-p_euler)*4)/4 # rounding and ensure divide by 2

        P.append(pp.euler(RPulley, eulerAngle, p=p_euler, num_pts=1800))
        x_pulley = P.xsize
        P.rotate(-eulerAngle / 2)
        P.x = 0

        ortho_bend = pp.euler(R_min, 90-eulerAngle, p=0.3, num_pts=1800)
        alpha = np.deg2rad(eulerAngle)
        dx = RPulley + 5 - x_pulley + RPulley * np.sin(p_euler*alpha/2) - ortho_bend.ysize
        if dx > 0:
            P.append(pp.straight(dx/np.cos(np.deg2rad(eulerAngle)), num_pts=2))
        #P.append(ortho_bend)

        rotate_angle = eulerAngle / 2

    else:
        ll = np.round(d_safe * np.sqrt((4*RPulley/d_safe-1)), decimals=2)
        P.append(pp.straight(length=ll))
        P.x = 0

    return P, rotate_angle

########################################################################################################################
def raceTrackCouplerPath(paramsTable):
    if paramsTable['Lc'] <= 0:
        raise NameError('The coupler length Lc for a racetrack should be greater than zero')

    Dx = 2*np.sqrt(d_safe * (2*R_min - d_safe))

    P = Path()
    P.append([
        transitionS(0, 0, Dx, d_safe),
        pp.straight(paramsTable['Lc'], num_pts=3),
        transitionS(0, 0, Dx, -d_safe),
    ])

    return P

########################################################################################################################
def connect_element(element, paramsTable, nInd):
    ##############################
    # Check device type and load / create
    ##############################
    # _________________________________
    # Ring
    if paramsTable['Type'] in 'ring':
        element = addRingCoupler(element, paramsTable)

    elif paramsTable['Type'] in ['racetrack', 'snail', 'square']:
        element = addRingCoupler(element, paramsTable)

    # _________________________________
    # GDS ring
    elif paramsTable['Type'] == 'gdsRing':
        # Declare the geometrical parameters ! [still assumes a circle geometry afterward especially for pulley]
        paramsTable['RR'] = (element.ymax - element.ymin) / 2
        paramsTable['RW'] = 0
        element = addRingCoupler(element, paramsTable)

    # _________________________________
    # GDS file with ports defined in a yaml file
    elif paramsTable['Type'] == 'gdsElement':
        # load the port config file
        portFile = paramsTable['gdsPortConfig']
        if not(isinstance(portFile, str)):
            portFile = str(paramsTable['gdsFile'])

        with open('./ExternalGDS/' + portFile + '.yaml') as f:
            portConfig = yaml.load(f, Loader=loader)
        portConfig = portConfig['ports']

        # Setup the ports
        for curr_port in portConfig:
            if re.match('^in.*', curr_port['name']):
                orient = 180
            else:
                orient = 0
            element.add_port(name=curr_port['name'], midpoint=(curr_port['x'] + element.x, curr_port['y'] + element.y),
                                width=curr_port['width'] + 2*paramsTable['offset'], orientation=orient)

    # _________________________________
    # Simple Waveguide
    elif paramsTable['Type'] == 'waveguide':
        print('Waveguide object has existing connectors')

    # _________________________________
    # Undefined type
    else:
        raise NameError('Unknown Type %s in paramsTable' % str(paramsTable['Type']))

    ##############################
    # Export to svg if toggle on
    ##############################
    if args.svg:
        element.write_svg(os.path.join(
            pathName, designFileName + str(nInd) + '.svg'), background=None)

    ##############################
    # Export to GDS if toggle on
    ##############################
    if args.subGDS:
        fname = os.path.join(pathName, designFileName + '_' +
                             'Dev%i' % paramsTable['Dev'])

        element.write_gds(fname + '.gds')

        portConfig = [{'name': k,
                       'x': float(element.ports[k].x - element.x),
                       'y': float(element.ports[k].y - element.y),
                       'width': float(element.ports[k].width)} for k in element.ports.keys()]
        portConfig = {'ports': portConfig}

        with open(fname + '.yaml', 'w') as file:
            yaml.dump(portConfig, file, default_flow_style=False, canonical=False)

    return element


########################################################################################################################
def createDie(size, street_width = 10):
    size = np.roll(np.array(size), 1)  # Swap the 2 orientations to account for later rotation
    die = Device(name='chipDie')
    inrect = die << pg.rectangle(size=size, layer=CHIP)
    inrect.center = (0, 0)

    outrect = die << pg.rectangle(size=size + np.array([1, 1]) * 2 * street_width, layer=AREA)
    outrect.center = (0, 0)

    die.rotate(90)

    # frame = pg.boolean( A=outrect,
    #                    B=inrect,
    #                    num_divisions=[1, 1],
    #                    operation='not',
    #                    layer=layer)
    # frame.name = 'Die'
    return die


########################################################################################################################
#    This is the actual part of the code to place the rings & connect the waveguides
#####################################

# Loop over the design list
for iDesign in range(len(runInfo['designList'])):

    # Get the chip specific info
    chipInfo = runInfo['designList'][iDesign]

    # Complete with the run default chip parameters
    chipInfo = {**runInfo['defaultChipParam'], **chipInfo}

    ######################
    # Get parameters from run description file
    # Chip parameters
    LxChip = chipInfo['LxChip']  # Chip width
    LyChip = chipInfo['LyChip']  # Chip height
    # To shift the placement wrt the ebeam sides
    placeMarginH = chipInfo['placeMarginX']
    placeMarginV = chipInfo['placeMarginY']
    placeMarginT = chipInfo['placeMarginT']
    # Width of the inversion in case positive photoresist
    wInvert = chipInfo['wInvert']

    # Added margin away from resonator in the straight to pulley transition
    pulleyBuffer = chipInfo['pulleyBuffer']

    ######################
    # Get the design table file
    designFileName = chipInfo['design']
    chipBaseName = runInfo['runName'] + '_' + designFileName
    designTable = pd.read_excel('./DesignsTables/' + designFileName + '.xlsx')
    #designTable = designTable.dropna(how='all', axis=1)
    designTable.insert(len(designTable.columns), 'offset', chipInfo['geomOffset']) # cast the offset info

    if 'gdsFile' not in designTable:
        designTable['gdsFile'] = np.nan

    print('Design %s' % chipBaseName)

    devices_group = Group()

    # Instanciate the devices (avoid duplicates)
    print('++++ Creating devices ++++')
    devParameters = ['Type', 'gdsFile', 'RR', 'RW', 'm', 'APhC', 'offset', 'PhCProfile', 'PhCSquareness']
    grp = designTable.reset_index().groupby(devParameters, dropna=False, sort=False)

    ringTblIdx = list(grp.first()['index'])
    ringID = list(grp.ngroup())

    devicesList = []
    for iR in ringTblIdx:
        ring = create_element(designTable.iloc[iR, :])
        devicesList.append( ring )

    # Generate a dictionary of tapers from Ligentec
    taperLUT = np.array([1])
    taperLUT = pd.DataFrame({'width': taperLUT})
    taperLUT['obj'] = taperLUT.apply(lambda x: ligenTaperCoupler(x['width']), axis=1)

    # Loop over the devices
    print('++++ Placing devices references ++++')
    
    for ii in range(len(designTable)):
        ######################
        # resonator creation
        print('Dev %i -> ref %i' %(ii, ringID[ii]) )
        devName = '%03i_device%i'%(ii, designTable['Dev'][ii])
        curr_elmt = Device(name=devName)
        curr_elmt << devicesList[ringID[ii]]
        curr_elmt = connect_element(curr_elmt, designTable.iloc[ii], ii)

        # Add a meander to go to other side of the current element
        dvcOutPort = next((x for x in curr_elmt.get_ports() if x.name == 'out'), None)
        wrapR = calc_Rwrap(curr_elmt)

        X = CrossSection().add(width=dvcOutPort.width, layer=SiNMAIN, ports=('in', 'mid'))
        P = Path()
        bend_path = pp.euler(R_min, 90 - dvcOutPort.orientation, p=0.3, num_pts=1800)
        dx = curr_elmt.xmax + dvcOutPort.width + placeMarginH/2 - dvcOutPort.x - bend_path.xsize
        if dx > 0:
            P.append(pp.straight(dx, num_pts=2))
        P.append(bend_path)
        bend = P.extrude(X)
        bend.name = 'outTurn'
        bend = curr_elmt << bend
        bend.connect('in', dvcOutPort)
        curr_elmt.add_port(bend.ports['mid'])
        # else:
        #     curr_elmt.ports['out'].name = 'mid'
        #     curr_elmt.ports['mid'] = curr_elmt.ports.pop('out')

        curr_elmt.add_port( # add a connector for the future
            name='out_offset',
            midpoint=np.round((curr_elmt.ports['mid'].x + R_min * 1.2,
                               np.max([curr_elmt.ymax, curr_elmt.ports['mid'].y + R_min + 2])),
                              decimals=2), # rounding helps w/ discretization later
            width=dvcOutPort.width,
            orientation=0
        )

        # Mirror the element to place the coupler on top
        curr_elmt.mirror(curr_elmt.center, curr_elmt.center + (1, 0))

        # Add the element to the global group
        devices_group.add(curr_elmt)

    # TODO: Cluster the devices based on a height threshold difference. Keep the original order in the clusters
    # See https://stackoverflow.com/questions/11513484/1d-number-array-clustering for example, but need to keep the order
    # # Reorder the device group based on height
    # heights = [x.ysize for x in devices_group.elements]
    # # order = np.argsort(areas, kind='mergesort')
    # heights = sorted([thing for thing in enumerate(heights)], key=lambda x: x[1], reverse=True)
    # order = [x[0] for x in heights]
    # devices_group.elements = [devices_group.elements[i] for i in order]
    # designTable = designTable.iloc[order].reset_index(drop=True)

    # Split into chips
    typicalDevHeight = np.median([X.ysize for X in devices_group.elements])
    placeWidth = LxChip - 2 * 400 - 2 * R_min

    # Instantiate a chip device
    ii_split = 0
    chip_idx = 0

    chipName = chipBaseName
    current_chip = Device(name=chipName)

    # Distribute into rows
    refs = current_chip.add_ref(devices_group.elements)  # Add the references to the chip
    remaining_idx = [*range(len(refs))]
    order_map = []
    default_margin = R_min
    h_max = default_margin
    rID = 0
    rowIDs = []
    curr_xpos = 0
    currRow_idx = 0
    while len(remaining_idx) > 0:
        for ii, idx in enumerate(remaining_idx):
            elt = refs[idx]
            elt_width = elt.xsize
            if currRow_idx % 2 == 0 and designTable.Type[idx] == 'snail': ## If a snail resonator, 
                elt_width += -R_min
            if curr_xpos + elt_width + placeMarginH < placeWidth - 2 * h_max:
                if elt.ysize > default_margin:
                    h_max += placeMarginH/2 #0.01*elt.xsize
                curr_xpos += elt_width + placeMarginH
                order_map.append(remaining_idx.pop(ii))
                rowIDs.append(rID)
                currRow_idx += 1
                break
            if ii == len(remaining_idx)-1:
                print("creating new row")
                curr_xpos = 0
                currRow_idx = 0
                rID += 1
                h_max = default_margin

    refs = np.array([refs[i] for i in order_map])
    designTable = designTable.iloc[order_map].reset_index(drop=True)
    rowIDs = np.array(rowIDs)


    # Create the list of device labels objects
    lblLUT = {}
    for idx, ref in enumerate(refs):
        ref.origin = np.round(ref.origin, decimals=2) # Rounding to reduce connection issues with the 5nm grid ??
        if idx % 5 == 0:
            lb = create_sin_txt(str(idx), labelsFontSize)
            lb.name = 'label%i' % idx
        else:
            lb = None
        lblLUT.update({ref.parent.name: lb})  # associate a label

    # Group that will gather all the rows
    rowsGroup = Group()

    for rwID in range(rowIDs[-1]+1):
        curr_row = Group()
        curr_row.add( refs[rowIDs == rwID].tolist() )
        curr_row_designs = designTable.iloc[rowIDs == rwID, :].reset_index()
        print('Content of row %i' % rwID)
        print(curr_row_designs)

        curr_row.distribute(direction='x', spacing=placeMarginH)

        for idx, elt in enumerate(curr_row.elements):
            dvc = elt.parent
            turnA = next((x for x in dvc.references if x.parent.name == 'outTurn'), None)
            if len(curr_row.elements) == 1 or (idx % (len(curr_row.elements)-1)) == 0:
                # if first or last in row, delete the turn and move the out port
                dvc.remove(dvc.ports['out_offset'])
                if turnA is not None:
                    # elt.movex(turnA.xsize)
                    # [X.movex(turnA.xsize) for X in curr_row.elements[idx+1:]]
                    xM = dvc.xmax
                    dvc.remove(turnA)
                    if idx == 0:
                        dvc.xmax = xM
                    dvc.ports['out'].name = 'out_offset' # need to rename the ref port and not the dvc (no update)
                    dvc.ports['out_offset'] = dvc.ports.pop('out')
                else:
                    dvc.ports['mid'].name = 'out_offset'  # need to rename the ref port and not the dvc (no update)
                    dvc.ports['out_offset'] = dvc.ports.pop('mid')

                angle_rotate = dvc.ports['out_offset'].orientation/2

                if angle_rotate != 0:
                    dvc.rotate(-angle_rotate, center=dvc.center)
                    turnPath = pp.euler(radius=R_min, angle=angle_rotate, p=0.3, num_pts=1800)
                    XC = CrossSection().add(width=dvc.ports['out_offset'].width, layer=SiNMAIN, ports=('out', 'in'))
                    turnA = dvc << turnPath.extrude(XC)
                    turnA.connect('out', dvc.ports['in'])
                    dvc.ports['in'] = turnA.ports['in']  # need to rename the ref port and not the dvc (no update)
                    dvc.ports['in'].orientation = 180
                    dvc.absorb(turnA)

                    turnB = dvc << turnPath.extrude(XC)
                    turnB.connect('in', dvc.ports['out_offset'])
                    dvc.ports['out_offset'] = turnB.ports['out']
                    dvc.ports['out_offset'].orientation = 0
                    dvc.absorb(turnB)
            else:
                # close the connection gap in the other devices
                turnB = dvc << pr.route_smooth(
                    dvc.ports['out_offset'],
                    dvc.ports['mid'],
                    radius=R_min,
                    path_type='L',
                    smooth_options={'num_pts': 1800, 'p': 0.3, 'use_eff': True}, #,
                    layer=SiNMAIN
                )
                if turnA is not None:
                    dvc.absorb(turnA)
                dvc.absorb(turnB)

        curr_row.distribute(direction='y', spacing=placeMarginV, separation=True, edge='ymin')
        ypos = 0
        y_max = 0
        eltList = curr_row.elements
        for idx, elt in enumerate(eltList):
            xshift = 0
            if curr_row_designs.Type[idx] == 'snail': # special case when dealing with 2 consecutive snails
                if idx % 2 == 0 and idx < len(eltList) and curr_row_designs.Type[idx+1] == 'snail':
                    elt.rotate(180, center = elt.center)
                    if idx > 0:
                        elt.movex(-R_min)
                    xshift = -1.8 * R_min

            else:
                if idx == 0:
                    #elt.mirror(elt.center, elt.center + (1, 0))
                    elt.rotate(180, center = elt.center)

            for ii in range(idx+1, len(eltList)):
                eltList[ii].movex(xshift)

            elt.ymin = ypos
            if elt.ymax < y_max + placeMarginV:
                elt.ymax = y_max + placeMarginV
            ypos = np.maximum(elt.ymin, np.min([x.y for x in elt.ports.values()])) + placeMarginV
            y_max = elt.ymax

        # Add the current row to the global group
        rowsGroup.add( curr_row )

    rowsGroup.align(alignment='x') # center the rows

    # Center the devices on the chip
    chip_die = current_chip << createDie(size=(LxChip, LyChip))  # Create the die object
    chip_die.center = rowsGroup.center
    #rowsGroup.movex(-meanDevHeight/2)

    # Loop over the rows and choose the taper and connect w/ waveguides
    for curr_row in rowsGroup.elements:
        row_bbox = curr_row.bbox
        for devPortType in ['in', 'out_offset']:
            listToConnect = []
            allPorts = [list(X.ports.values()) for X in curr_row.elements]
            allPorts = list(chain.from_iterable(allPorts))
            portList = [x for x in allPorts if x.name == devPortType ]
            for curr_port in portList:
                curr_taper_group = Group()  # new taper to group taper + label
                # Find the matching device label and add it to the taper device
                devName = curr_port.parent.parent.name
                if lblLUT[devName] is not None:
                    lbl_ref = current_chip << lblLUT[devName]
                    curr_taper_group.add(lbl_ref)
                # Find the closest taper width to the port and add it to the chip
                taper_ref = current_chip << taperLUT.obj[np.abs(taperLUT.width - curr_port.width).idxmin()]
                curr_taper_group.add(taper_ref)
                curr_taper_group.align(alignment='x')  # align in x center
                curr_taper_group.distribute(direction='y', spacing=3)
                if len(curr_taper_group.elements) >1:
                    lbl_ref.movey(placeMarginT-3) # Dirty ... for now align in ymax and we'll shift back after

                if np.cos(np.deg2rad(curr_port.orientation)) > -0.1:
                    curr_taper_group.xmax = chip_die.xmax
                else:
                    curr_taper_group.xmin = chip_die.xmin
                    taper_ref.rotate(180, center=taper_ref.center)

                curr_taper_group.movey(curr_port.y)

                curr_row.add(curr_taper_group.elements)
                listToConnect.append([taper_ref, curr_port, curr_taper_group])

            # Now Let's distribute the tapers along the row height

            rowCouplers = Group([elt[2] for elt in listToConnect])  # make a new group with all tapers groups
            rowCouplers.distribute(direction='y', spacing=placeMarginT)
            rowCouplers.y = np.mean(row_bbox[:, 1]) # center the tapers globally to the row of devices
            # check is the tapers are smaller than the height of the column if so spread them apart evenly
            rch = rowCouplers.ysize
            if len(rowCouplers.elements) > 1 and rch < np.diff(row_bbox[:, 1]):
                for n_elt, elt in enumerate(rowCouplers.elements):
                    if n_elt == 0:
                        y0 = elt.y
                    elt.y = (elt.y - y0) / rch * (row_bbox[1,1] - row_bbox[0,1]) + row_bbox[0,1]

            for elt in rowCouplers.elements:
                y_tmp = elt.ymax # store the max position
                elt.distribute(direction='y', spacing=3)
                elt.ymax = y_tmp
                for dvc in elt.elements:
                    elt.y = round(elt.y, 2)

            for idPort, portPair in enumerate(listToConnect):  # taper then resonator port in the pair
                tpr = portPair[0]
                tprPort = tpr.ports['invTaperIn']
                dvcPort = portPair[1]
                dvc = dvcPort.parent
                print('Connecting %s port %s' % (dvc.parent.name, dvcPort.name))

                # Add a straight section in the tpr port in order to fully overlap w/ the BB port
                tpr_wg_in = current_chip << pg.straight(size=(tprPort.width, 6), layer=SiNMAIN)
                tpr_wg_in.connect(port=1, destination=tprPort)
                tpr_wg_in.parent.name = 'toFlatten'
                curr_row.add(tpr_wg_in)
                tprPort = tpr_wg_in.ports[2]

                # This is the code for the euler + straight transition instead of sbend
                #P_trans.end_angle = 180
                # Dx = x_mid - (tprPort.x + dx_connect)
                # bendAngle_rad = np.arctan(Dy/(Dx+R_min*dx_dir))
                # bendAngle_deg = np.rad2deg(bendAngle_rad)
                # print(bendAngle_deg)
                # if np.abs(bendAngle_deg) < 60:
                # else:
                #     L = (np.abs(Dy) - 2 * R_min * (1 - np.cos(bendAngle_rad)))/np.sin(bendAngle_rad)
                #     P_trans.append([
                #         pp.straight(np.abs(dx_connect), num_pts=2),
                #         pp.euler(angle=bendAngle_deg, radius=R_min, use_eff=True),
                #         pp.straight(L, num_pts=2),
                #         pp.euler(angle=-bendAngle_deg, radius=R_min, use_eff=True),
                #     ])

                if np.round(np.abs(dvcPort.orientation - tprPort.orientation)) == 180:
                    Dy = dvcPort.y - tprPort.y
                    dx_dir = np.cos(np.deg2rad(tprPort.orientation))
                    x_mid = row_bbox[0, 0] if tprPort.orientation == 0 else row_bbox[1, 0]
                    x_mid += dx_dir * np.abs(Dy) * 0.4
                    x_mid = dx_dir * np.min([dx_dir * x_mid, dx_dir * dvcPort.x])

                    intermdPort = Port(midpoint=(x_mid, dvcPort.y),
                                       orientation=tprPort.orientation,
                                       width=dvcPort.width)

                    pts = transitionS(
                        tprPort.x, tprPort.y, intermdPort.x, intermdPort.y,
                        t0=tprPort.orientation,
                        t1=tprPort.orientation)
                    pts = pts - pts[0]
                    P_trans = Path(pts)
                    dx = np.abs(intermdPort.x - dvcPort.x)
                    if dx != 0:
                        P_trans.append(pp.straight(dx, num_pts=round(dx/2)))

                else:
                    # This part should ne be used anymore as all angles are back to 0, 90 degs
                    XC = CrossSection()
                    XC.add(width=dvcPort.width, layer=SiNMAIN, ports=('in', 'inter'))
                    turnA = dvc.parent << pp.euler(
                        radius=R_min,
                        angle=(135-np.mod(tprPort.orientation + dvcPort.orientation, 360)),
                        p=0.3,
                        num_pts=1800).extrude(XC)
                    turnA.connect('in', dvc.parent.ports[dvcPort.name])
                    dvc.parent.add_port(turnA.ports['inter'])
                    prt_dvc = dvc.ports['inter']
                    pts = transitionS(
                        tprPort.x, tprPort.y, prt_dvc.x, prt_dvc.y,
                        t0=tprPort.orientation,
                        t1=180+prt_dvc.orientation)
                    pts = pts - pts[0]
                    P_trans = Path(pts)


                X1 = CrossSection().add(
                    width=tprPort.width,
                    layer=SiNMAIN, name='wg',
                    ports=('in1', 'out1')
                )
                X2 = CrossSection().add(
                    width=dvcPort.width,
                    layer=SiNMAIN, name='wg',
                    ports=('in1', 'out1')
                )
                X_trans = pp.transition(
                    cross_section1=X1,
                    cross_section2=X2,
                    width_type='sine'
                )
                S_curve = P_trans.extrude(X_trans)
                S_curve.name = 'toFlatten'
                S_curve = current_chip << S_curve
                S_curve.connect('in1', tprPort)
                curr_row.add(S_curve)
                #current_chip.absorb(S_curve)

    # Distribute the rows
    rowsGroup.distribute(direction='y', spacing=placeMarginT, separation=True)  # Spacing between the rows

    # Discretize the position a bit (may help with the 5nm grid??)
    for row in rowsGroup.elements:
        row.center = np.round(row.center, decimals=2)

    # Insert chip label
    chip_lbl = create_sin_txt(chipName.replace('_', ' '), chipFontSize)
    chip_lbl.name = 'ChipLabel'
    chip_lbl = current_chip << chip_lbl
    chip_lbl.ymax = rowsGroup.elements[0].ymin - 3
    chip_lbl.xmin = chip_die.xmin + 60
    rowsGroup.add(chip_lbl)

    # Center the devices on the chip
    rowsGroup.center = chip_die.center

    refLst = [x for x in current_chip.references if 'toFlatten' in x.parent.name]
    for ref in refLst:
        current_chip.absorb(ref)
    for ref in refs:
        current_chip.add(ref.get_polygonsets(depth=0))
        while ref.parent.polygons != []:
            ref.parent.remove(ref.parent.polygons)

    current_chip.absorb(chip_die)

    current_chip.rotate(90)
    current_chip.move((-current_chip.xmin, -current_chip.ymin))

    print("++++ Writing chip %s ++++" % chipName)
    current_chip.write_gds(
        os.path.join(pathName, chipName + '.gds'),
        precision=5e-09,
        cellname='TOP'
    )

    # export the design table of the chip as an excel file
    designTable.iloc[ii_split:ii_split+len(refs), :].to_excel(
        os.path.join(pathName, chipName + '.xlsx'),
        index=True
    )
    ii_split += len(refs)

plt.show()