clearvars
close all
clc

% Relation APhC - Gamma
%Aphc2Gamma_pp = [6.0250e-09 -3.5046e-06 -1.5759e-04 0.3035 -0.6057];
%gamma2APhC = @(G, W) (G + 0.1)/(0.1652*2.2/W); % Rescale the effect wrt RW??
aphc2gamma_slope = @(RW) 0.1652 * exp(2.4*(2.2./RW - 1));
aphc2gamma_offset = @(RW) 0.2 ./ RW;
gamma2APhC = @(G, W) (G - aphc2gamma_offset(W)) ./ aphc2gamma_slope(W);

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% % flatCombTarget on blue branch
% % load the normalized coupling profile file
% fName = 'Coupling_flatComb_blueBranch';
% 
% % PhC of the pump in normalized units
% PumpExtraNormSplitting = (-31:1.5:-21);
% 
% % Define the linewidth
% %lnWdths = linspace(100, 240, 7) * 1e-3 % small coupling gap
% lnWdths = [150, linspace(200, 300, 6)] * 1e-3 % Bigger coupling
% 
% % Geometry
% RR = [109; 109];
% RW = [2.35; 2.3];
% m = [1628; 1626];
% geomTable = table(RR, RW, m);

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% % flatCombTarget on red branch
% % load the normalized coupling profile file
% fName = 'Coupling_flatComb_redBranch';
% 
% % PhC of the pump in normalized units
% PumpExtraNormSplitting = unique([linspace(24, 30, 4), linspace(30, 40, 5)]);
% 
% % Define the linewidth
% %lnWdths = linspace(120, 250, 6) * 1e-3 % small coupling gap
% lnWdths = linspace(200, 300, 7) * 1e-3 % Bigger coupling
% 
% % Geometry
% RR = [109; 109];
% RW = [2.35; 2.3];
% m = [1628; 1626];
% geomTable = table(RR, RW, m);

% %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% % Gaussian comb target
% % load the normalized coupling profile file
% fName = 'gaussianCombTarget';
% 
% % PhC of the pump in normalized units
% PumpExtraNormSplitting = unique([3:2:5, 0:2:12]); % Remember to put extra, overshooting coupling is a bit tricky
% 
% % Define the linewidth
% %lnWdths = [100, linspace(200, 700, 7)] * 1e-3 % small coupling gap (???)
% lnWdths = [200, linspace(400, 1000, 7)] * 1e-3 % Bigger coupling
% 
% % Geometry
% RR = [109.8; 109.5];
% RW = [1.3; 1.3];
% m = [1588; 1582];
% geomTable = table(RR, RW, m);

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% Gaussian comb target 400 GHz
% load the normalized coupling profile file
fName = 'Coupling_gaussianTarget_halfBW';

% PhC of the pump in normalized units
PumpExtraNormSplitting = unique([3:2:5, 0:2:10]); % Remember to put extra, overshooting coupling is a bit tricky

% Define the linewidth
%lnWdths = linspace(100, 400, 7) * 1e-3 % small coupling gap (???)
lnWdths = linspace(150, 500, 7) * 1e-3 % Bigger coupling

% Geometry
RR = [53.15; 53.6];
RW = [1.55 ; 1.55];
m =  [778  ; 785];
geomTable = table(RR, RW, m);

%% Main code
% Load reference file
coupling_ref = ;
mu = coupling_ref.mu;
coupling_ref = coupling_ref.normalizedCoupling;

% discard zeros
mu(coupling_ref < eps) = [];
coupling_ref(coupling_ref < eps) = [];

fileTemplate =  [fName '_P%g'];
pump_rel_gamma = zeros(size(PumpExtraNormSplitting));
APhC = zeros(length(PumpExtraNormSplitting), length(lnWdths), length(geomTable.RW));

for iA = 1:length(PumpExtraNormSplitting)
    
    couplingNorm = coupling_ref;
    % Add the pump shift
    couplingNorm(mu==0) = max(0, couplingNorm(mu==0) + PumpExtraNormSplitting(iA));
    
    % Normalize to the peak and compute the main APhC
    Gamma_norm = max(couplingNorm(mu~=0));
    pump_rel_gamma(iA) = round( couplingNorm(mu==0) / Gamma_norm, 3);
    couplingNorm = couplingNorm / Gamma_norm;
    
    tbl = array2table([mu, couplingNorm], 'VariableNames', {'mu', 'APhCNorm'});
    
    % Add a phase variation to avoid too strong amplitude variations of
    % the profile
    
    mm = max(m);
    thi = linspace(-pi,pi, round(15*(mm+2*max(tbl.mu))));
    PhC_Profile = @(C) sum(0.5*(real(tbl.APhCNorm(:))*ones(size(thi))) .* cos( (mm + 2*tbl.mu(:)).*thi + C * tbl.mu(:).^2) ,1);
    totAmp = @(X) max(X) - min(X);
    [C, minAmp] = fminsearch(@(C) totAmp(PhC_Profile(C)), pi/(max(tbl.mu)*sqrt(7)));
    
    tbl.APhCNorm = tbl.APhCNorm + 1i * C/2 * tbl.mu;
    
    % generate the file
    writetable(tbl, sprintf([fileTemplate '.txt'], pump_rel_gamma(iA)))
    
    %% Compute the PhC scaling depending on linewidth
    % Convert Normalized coupling into APhC
    couplingAbs = Gamma_norm * lnWdths/2;
    %APhC_profile = arrayfun( @(G) fzero(@(x) polyval(Aphc2Gamma_pp(RW), x) - G, (G + 0.1)/0.1652), couplingAbs);
    
    %% Set a linewidth and the normalized gamma PhC will be denormalized and the APhC calculated
    % compute the combinations of values
    %combineArr = allcomb(APhC, muT, AT);
    
    % Convert absolute coupling coef to geometrical APhC (and append the
    % RW)
    APhC(iA, :, :) = gamma2APhC(couplingAbs, geomTable.RW)';
    
    %% Plot the profile
    APHC_M = max(APhC(iA, :), [], 'all');
    
    figure(iA)
    t=tiledlayout(2,2,'TileSpacing', 'compact');
    nexttile
    plot(mu, couplingNorm * Gamma_norm, 'k.'),
    ylabel('\gamma_{norm}')
    xlabel('\mu')
    nexttile
    plot(mu, couplingNorm * APHC_M, 'k.'),
    ylabel('APhC_{Max} (nm)')
    xlabel('\mu')
    title(sprintf('\\kappa = %g MHz', 1e3*max(lnWdths)))
    
    nexttile([1,2])
    disp(C)
    mPhC = mm + 2*tbl.mu(:);
    plot(thi, 0.5 * APHC_M * sum(real(tbl.APhCNorm(:)) .* cos( mPhC .* thi + mPhC .* imag(tbl.APhCNorm(:)) ) ,1))
    axis tight
    hline(minAmp * APHC_M/2)
    ylabel('\rho (nm)')
    xlabel('\phi')
    
    
end

% round the APhC values
APhC = round(APhC, 2);


%% Combine the parameters into a list of design array
pump_rel_gamma = repmat(pump_rel_gamma', 1, length(lnWdths), height(geomTable));
geom_Idx = permute(repmat((1:length(RW))', 1, length(PumpExtraNormSplitting), length(lnWdths)), [2,3,1]);

combineTable = array2table( [APhC(:), pump_rel_gamma(:)], ...
    'VariableNames', {'APhC', 'Profile'});
combineTable.APhC = combineTable.APhC * 1e-3;
combineTable = [geomTable(geom_Idx(:), :), combineTable];
combineTable = unique(combineTable); % keep unique values only
combineTable = sortrows(combineTable, {'Profile', 'APhC', 'RW'});
combineTable.Profile = compose(fileTemplate, combineTable.Profile);

disp(combineTable)
fprintf('%d combinations in total\n', size(combineTable, 1))

%% Export
delete('currentTable.xlsx')
writetable(combineTable, 'currentTable.xlsx')
system('open currentTable.xlsx')
