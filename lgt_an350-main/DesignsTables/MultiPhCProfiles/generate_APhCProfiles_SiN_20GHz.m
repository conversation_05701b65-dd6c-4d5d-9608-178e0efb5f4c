clearvars
close all
clc

%% Define the spectral profile
rw = 2.2; % targeted ring width in um
linewidth = 57e-3; % In GHz TODO: spread of values
D_2_GHz = -250e-6; % TODO: spread of values
gamma_norm_shift = 8; % TODO: Sweep

m_0 = 16276; % N corrug. at the center mode (longitudinal mode # m_0/2)

mu = [-1,1]*65;
gamma_shift_GHz = linewidth/2 * gamma_norm_shift; % TODO: Sweep
gamma_GHz = abs(D_2_GHz)*mu.^2 + gamma_shift_GHz; % Remember, to compensate dispersion, no need to divide D2 by 2 in the full bandgap
gamma_GHz(mu == 0) = 0;

profileName = sprintf('SCC_offsetPhC_N%d_S%g.txt', max(mu), gamma_shift_GHz);


%% Initial checks
if mod(m_0,2)~=0
    error("m_0 must be even")
end

eps_GHz = gamma_GHz/2;

% plot the projected dispersion
figure
tl = tiledlayout(2,1);
tl.Padding = "tight";
nexttile
plot(mu, gamma_GHz, '.-')

nexttile
hold on
plot(mu, D_2_GHz/2*mu.^2 + eps_GHz, 'bo')
plot(mu, D_2_GHz/2*mu.^2 - eps_GHz, 'ro')
drawnow()
fplot(@(x) D_2_GHz/2*x.^2, xlim, '-')

%% Load the calibration data
%load('/home/<USER>/Nextcloud/Partages reçus/ICB - DESY file exchange/data/20240417_splitting_calibration.mat')
%eps2APhC = griddedInterpolant(splitting*1e-9, dw, 'pchip'); % Yields corrug amplitude in um Warning DESY uses half bandgap
%eps2APhC = @(x) 86e-3/(10.8/2) * x;
tblpolyFitphc = readtable('./phCPolycoefs_AN350-01.xlsx');
Id_rw = knnsearch(tblpolyFitphc.RW, rw);
P = tblpolyFitphc{Id_rw, 2:end}; % CAUTION this is gamma
gamma2APhC = @(x) fzero(@(y) polyval(P, y) - x, 5);

%% Convert the profile to geometry with chirp to minimize amplitude variation
A_PhC = zeros(size(gamma_GHz));
for ii = 1:length(gamma_GHz)
    if gamma_GHz(ii) == 0
        A_PhC(ii) = 0;
    else
        P_i = P;
        P_i(end) = P_i(end) - gamma_GHz(ii);
        A_i = roots(P_i);
        A_PhC(ii) = max(real(A_i)); % Hmm dangerous ...
    end
end

A_PhC = 1e-3 * round(A_PhC); % rounding to nm

% Check graphically that the returned solution is not nonsense
figure
tiledlayout
nexttile
hold on
fplot(@(x)polyval(P, x), [0,100])
plot(A_PhC*1e3, gamma_GHz, 'o')
xlabel('A_{PhC} (nm)')
ylabel('Total splitting \Gamma (GHz)')

nexttile
plot(mu, A_PhC, 'o')
xlabel('Mode number')
ylabel('A_{PhC} (\mum)')


A_PhC_max = max(A_PhC);

tbl = array2table([mu ; A_PhC / A_PhC_max]', 'VariableNames', {'mu', 'APhCNorm'});

%% Compute the spatial profile with optimum chirp

thi = linspace(0, pi, round(11*(m_0 + 2*max(tbl.mu)))); % only half of the period is enough (0,pi)
m_PhC = 2*tbl.mu(:);
PhC_Profile = @(C) sum(0.5*(real(tbl.APhCNorm(:))*ones(size(thi))) .* cos( (m_0 + m_PhC).*thi + C * m_PhC.^2) ,1);
totAmp = @(X) max(X) - min(X);

xx = linspace(0, 3, 5e3);
[minAmp, C] = min( arrayfun(@(C) totAmp(PhC_Profile(C)), xx) );
C = xx(C);
minAmp*A_PhC_max

[C, minAmp] = fminsearch(@(C) totAmp(PhC_Profile(C)), C);
minAmp*A_PhC_max


figure
plot(thi, PhC_Profile(C) * A_PhC_max)
hold on
plot(thi, PhC_Profile(0) * A_PhC_max)

tbl.phase = C * m_PhC;

writetable(tbl, profileName)

A_PhC_max
