% Side mode position
muT = 1;
muT(muT==0) = [];

% PhC of the pump in normalized units
NormSplitting = 9:15;

% side mode values (relative) to explore near the 'optimum'
AT_offset = (-1:.5:1)'/10;

% Define the linewidth
lnWdth = (230 + [-0.5, 1] * 65 )*1e-3 %[180, 220]*1e-3; %

strOut = '';

for iM = 1:length(muT)
    
    % The side mode coupling amplitude in fraction of the pump PhC
    lut = readtable("SideMode_vs_APhC.csv");
    sideModeRelation = griddedInterpolant(lut.gPhC, lut.SideMode, 'linear');
    
    AT = abs(sideModeRelation(NormSplitting) + AT_offset); %linspace(.25,.6,5);
    AT = AT(:);
    
    NormSplitting = repmat(NormSplitting, length(AT_offset), 1);
    NormSplitting = NormSplitting(:);
    
    
    %% generate the files
    fileTemplate = ['muPM' num2str(muT(iM)) '_A%g'];
    
    for jj = 1:length(AT)
        arr = [muT(iM) * [-1, 0, 1]', AT(jj) * [1, 0, 1]' + [0, 1, 0]'];
        tbl = array2table(arr, 'VariableNames', {'mu', 'APhCNorm'});
        
        % Add a phase variation to avoid too stron amplitude variations of
        % the profile
        tbl.APhCNorm = tbl.APhCNorm + 1i * [-1,0,1]'/sqrt(2);
        
        writetable(tbl, sprintf([fileTemplate '.txt'], AT(jj)))
    end
    
    %% Set a linewidth and the normalized gamma PhC will be denormalized and the APhC calculated
    APhC = 1e-3 * round( (NormSplitting .* lnWdth/2 + 0.0979)/0.1652 ); %(rounding to nm)
    AT = repmat(AT, 1, size(lnWdth,2));
    
    % compute the combinations of values
    %combineArr = allcomb(APhC, muT, AT);
    
    % concat the arrays otherwise
    combineArr = [APhC(:), AT(:)];
    combineArr = uniquetol(combineArr, 'ByRows', true) % keep unique values only
    
    fprintf('%d combinations in total\n', size(combineArr, 1))
    
    strOut = strcat(strOut, strjoin(compose("%g;" + fileTemplate, combineArr), '\n'));
    
end

clipboard('copy', strOut)