clearvars
close all
clc

%% Define the spectral profile
rw = 1.9; % targeted ring width in um
linewidth = 66e-3; % In GHz TODO: spread of values
D_2_GHz_base = -29.32e-3; % TODO: spread of values +- 3% ?
m_0 = 1616; % N corrug. at the center mode (longitudinal mode # m_0/2)


mu = -16:16; % Make a +-8 and +-16 version
mu(mu==0) = [];

D_2_GHz_relative_var_array = [-1, -0.5, 0, 1] * 0.04;
eps_shift_array = 4:2:10;
eps_rel_side_array = 1;
d2_extra_array = 0 % [-1, 0, 1] * 5e-2;

%% Assemble the sweep values

sweep_table = allcomb(D_2_GHz_relative_var_array, eps_shift_array, eps_rel_side_array, d2_extra_array);
sweep_table = array2table(sweep_table, "VariableNames", {'D_2_GHz_relative_var', 'eps_shift', 'eps_rel_side', 'd2_extra'});
sweep_table.PhCProfile(:) = "";
sweep_table.APhC(:) = 0;

%% Loop and generate the profiles

for tblIDX = 1:height(sweep_table)

    D_2_GHz = D_2_GHz_base * (1 + sweep_table.D_2_GHz_relative_var(tblIDX));
    eps_shift = sweep_table.eps_shift(tblIDX); % Sweep
    eps_rel_side = sweep_table.eps_rel_side(tblIDX); % Sweep
    d2_extra =  sweep_table.d2_extra(tblIDX); % sweep ??

    gamma_shift_GHz = linewidth/2 * 2 * eps_shift; % TODO: Sweep
    gamma_GHz = abs(D_2_GHz)*mu.^2 + gamma_shift_GHz + d2_extra/2 * linewidth/2 * (mu.^2-1); % Remember, to compensate dispersion, no need to divide D2 by 2 in the full bandgap
    gamma_GHz(mu == 0) = 0;
    gamma_GHz(abs(mu) == 1) = eps_rel_side * gamma_shift_GHz;

    profileName = sprintf('SCC_nullifyDispersion_D2MHz%g_KMHz%gN%d_epsShift%g_epsRelSide%g_D2Extra%g.txt',...
        round(D_2_GHz*1e3, 1), round(linewidth*1e3, 1), numel(mu),  eps_shift, eps_rel_side, d2_extra);


    %% Initial checks
    if mod(m_0,2)~=0
        error("m_0 must be even")
    end

    eps_GHz = gamma_GHz/2;

    % plot the projected dispersion
    fg = figure();
    fg.Position(3) = 3*fg.Position(3);
    tl = tiledlayout('flow');
    tl.Padding = "tight";
    nexttile
    plot(mu, gamma_GHz, '.-')
    ylabel('\Gamma (GHz)')
    xlabel('Mode number')

    nexttile
    hold on
    plot(mu, D_2_GHz/2*mu.^2, '-')
    plot(mu, D_2_GHz/2*mu.^2 + eps_GHz, 'bo')
    plot(mu, D_2_GHz/2*mu.^2 - eps_GHz, 'ro')

    ylabel('D_{int} (GHz)')
    xlabel('Mode number')

    %% Load the calibration data
    %load('/home/<USER>/Nextcloud/Partages reçus/ICB - DESY file exchange/data/20240417_splitting_calibration.mat')
    %eps2APhC = griddedInterpolant(splitting*1e-9, dw, 'pchip'); % Yields corrug amplitude in um Warning DESY uses half bandgap
    %eps2APhC = @(x) 86e-3/(10.8/2) * x;
    tblpolyFitphc = readtable('./phCPolycoefs_AN350-01.xlsx');
    Id_rw = knnsearch(tblpolyFitphc.RW, rw);
    P = tblpolyFitphc{Id_rw, 2:end}; % CAUTION this is gamma
    gamma2APhC = @(x) fzero(@(y) polyval(P, y) - x, 5);

    %% Convert the profile to geometry with chirp to minimize amplitude variation
    A_PhC = zeros(size(gamma_GHz));
    for ii = 1:length(gamma_GHz)
        if gamma_GHz(ii) == 0
            A_PhC(ii) = 0;
        else
            P_i = P;
            P_i(end) = P_i(end) - gamma_GHz(ii);
            A_i = roots(P_i);
            A_PhC(ii) = max(real(A_i)); % Hmm dangerous ...
        end
    end

    A_PhC = 1e-3 * round(A_PhC); % rounding to nm

    % Check graphically that the returned solution is not nonsense
    nexttile
    hold on
    fplot(@(x)polyval(P, x), [0,100])
    plot(A_PhC*1e3, gamma_GHz, 'o')
    xlabel('A_{PhC} (nm)')
    ylabel('Total splitting \Gamma (GHz)')

    nexttile
    plot(mu, A_PhC, 'o')
    xlabel('Mode number')
    ylabel('A_{PhC} (\mum)')


    A_PhC_max = max(A_PhC);

    tbl = array2table([mu ; A_PhC / A_PhC_max]', 'VariableNames', {'mu', 'APhCNorm'});

    %% Compute the spatial profile with optimum chirp

    thi = linspace(0, pi, round(11*(m_0 + 2*max(tbl.mu)))); % only half of the period is enough (0,pi)
    m_PhC = 2*tbl.mu(:);
    PhC_Profile = @(C) sum(0.5*(real(tbl.APhCNorm(:))*ones(size(thi))) .* cos( (m_0 + m_PhC).*thi + C * m_PhC.^2) ,1);
    totAmp = @(X) max(X) - min(X);

    xx = linspace(0, 3, 5e3);
    [minAmp, C] = min( arrayfun(@(C) totAmp(PhC_Profile(C)), xx) );
    C = xx(C);
    minAmp*A_PhC_max

    [C, minAmp] = fminsearch(@(C) totAmp(PhC_Profile(C)), C);
    minAmp*A_PhC_max

    nexttile
    plot(thi, PhC_Profile(0) * A_PhC_max)
    hold on
    plot(thi, PhC_Profile(C) * A_PhC_max)
    xlabel('phi (rad)')
    ylabel('\rho_{PhC} (\mum)')

    tbl.phase = C * m_PhC;

    writetable(tbl, profileName)
    sweep_table.PhCProfile(tblIDX) = strrep(profileName, '.txt', '');
    
    exportgraphics(fg, sweep_table.PhCProfile(tblIDX) + ".png")
    savefig(fg, sweep_table.PhCProfile(tblIDX) + ".fig")
    close(fg)

    sweep_table.APhC(tblIDX) = A_PhC_max;
end

disp(sweep_table)

sweep_table.phcsquareness(:) = 0;
writetable(sweep_table(:,{'APhC', 'phcsquareness', 'PhCProfile'}), 'temp.xlsx')