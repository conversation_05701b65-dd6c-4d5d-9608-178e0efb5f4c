<?xml version="1.0" encoding="utf-8"?>
<report-database>
 <description>Output DRC database</description>
 <original-file>/home/<USER>/Documents/Study/Codes/Chip_layout/build/gds/2147380034.oas</original-file>
 <generator/>
 <top-cell>Unnamed_21</top-cell>
 <tags>
  <tag>
   <name>waived</name>
   <description/>
  </tag>
  <tag>
   <name>red</name>
   <description/>
  </tag>
  <tag>
   <name>green</name>
   <description/>
  </tag>
  <tag>
   <name>blue</name>
   <description/>
  </tag>
  <tag>
   <name>yellow</name>
   <description/>
  </tag>
  <tag>
   <name>important</name>
   <description/>
  </tag>
 </tags>
 <categories>
  <category>
   <name>Rule 1 - DRC.X1PWidth</name>
   <description>X1P width must be ≥ 0.2 μm.</description>
   <categories>
   </categories>
  </category>
  <category>
   <name>Rule 2 - DRC.X1PSpacing</name>
   <description>X1P spacing must be ≥ 0.3 μm.</description>
   <categories>
   </categories>
  </category>
  <category>
   <name>Rule 3 - DRC.X1PSSCProtrusionLength</name>
   <description>SSC in X1P must protrude 10.0 μm from CHS.</description>
   <categories>
   </categories>
  </category>
  <category>
   <name>Rule 4 - DRC.X1BDensity</name>
   <description>X1B density must be ≤ 30% within a 4mm2 area.</description>
   <categories>
   </categories>
  </category>
  <category>
   <name>Rule 6 - DRC.X1PX1PINOverlap</name>
   <description>X1P must overlap X1PIN for proper Black Box connection.</description>
   <categories>
   </categories>
  </category>
  <category>
   <name>Rule 101 - DRC.CHSWidth</name>
   <description>CHS width must be ≥ 10.0μm.</description>
   <categories>
   </categories>
  </category>
  <category>
   <name>Rule 102 - DRC.CHSSpacing</name>
   <description>CHS spacing must be ≥ 10.0μm.</description>
   <categories>
   </categories>
  </category>
  <category>
   <name>Rule 103 - DRC.CHSSize</name>
   <description>The minimum size for a CHS layer is 2x2mm</description>
   <categories>
   </categories>
  </category>
  <category>
   <name>Rule 104 - DRC.CHSAspectRatio</name>
   <description>CHS has a maximum aspect ratio of 5.</description>
   <categories>
   </categories>
  </category>
  <category>
   <name>Rule 105 - DRC.CHSEnclosed50e</name>
   <description> X1P, X2P, BB, M1P, M1PAD, VIA, P1P, LC1, LC2, LCs, RibC layers must be ≥ 50 μm away from CHS edge.</description>
   <categories>
   </categories>
  </category>
  <category>
   <name>Rule 106 - DRC.CHSEnclosed10i</name>
   <description> X1P, X2P, BB, M1P, M1PAD, VIA, P1P, LC1, LC2, LCs, RibC layers must be ≥ 10μm from holes/TRN if they are  ≥ 100 μm away from CHS boundaries.</description>
   <categories>
   </categories>
  </category>
  <category>
   <name>Rule 107 - DRC.CHSWithinCSL</name>
   <description>CHS has to be enclosed by CSL.</description>
   <categories>
   </categories>
  </category>
  <category>
   <name>Rule 108 - DRC.CellContained</name>
   <description> X1P, X2P, BB, M1P, M1PAD, VIA, P1P, LC1, LC2, LCs, TRN, CHS layers must always be inside the CSL layer.</description>
   <categories>
   </categories>
  </category>
  <category>
   <name>Rule 1000 - DRC.ALLBDensity</name>
   <description>ALLB density must be ≤30% within a 4mm2 area.</description>
   <categories>
   </categories>
  </category>
  <category>
   <name>Rule 1001 - DRC.BBOverlap</name>
   <description>No layer apart from CHS and CSL must overlap with CellSize unless it does too with a BB pin.</description>
   <categories>
   </categories>
  </category>
 </categories>
 <cells>
  <cell>
   <name>Unnamed_21</name>
   <variant/>
   <layout-name/>
   <references>
   </references>
  </cell>
 </cells>
 <items>
 </items>
</report-database>
