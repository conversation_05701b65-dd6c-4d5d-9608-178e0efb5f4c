DSC_Report of Layout AN350_Ligentec_heater_2.gds
DSC call from the Call-button/Layout window

DSC Rule_01 - PASSED (TopCell Name Check)

DSC Rule_02 - PASSED (Database Unit Check)

DSC Rule_205 - FAILED (Origin Location Check)
The origin is not in the bottom left corner, please shift the design by:
 x-value:-50.0 µm
 y-value:-0.0 µm

DSC Rule_04(1) - FAILED (PDK Layer Compatibility Check)
---> PDK Layer Check (1) finished! Layers ["WAFER (999/0)", "WG (1/0)", "WGCLAD (111/0)", "SHALLOW_ETCH (2/6)", "SLAB90 (3/0)", "DEEP_ETCH (3/6)", "DEEPTRENCH (4/0)", "GE (5/0)", "UNDERCUT (6/0)", "WGN (34/0)", "WGN_CLAD (36/0)", "WG_ABSTRACT (1/5)", "NP (22/0)", "NPP (24/0)", "P (21/0)", "PP (23/0)", "PPP (25/0)", "GEN (26/0)", "GEP (27/0)", "HEATER (47/0)", "M2 (45/0)", "MTOP (49/0)", "VIA1 (44/0)", "VIA2 (43/0)", "PADOPEN (46/0)", "NO_TILE_SI (71/0)", "PADDING (67/0)", "DEVREC (68/0)", "FLOORPLAN (64/0)", "TEXT (66/0)", "WG_PIN (1/10)", "PORTE (1/11)", "PORTH (70/0)", "SHOW_PORTS (1/12)", "LABEL_INSTANCE (206/0)", "LABEL_SETTINGS (202/0)", "TE (203/0)", "TM (204/0)", "DRC_MARKER (205/0)", "SOURCE (110/0)", "MONITOR (101/0)"] are found! These layers do not exist in our library.
Please remove the layers prior to submission.

DSC Rule_04(2) - PASSED (CHS-CSL Layer Check)

DSC Rule_05- START (BBR Check)
If there wrongly instantiated BlackBoxes at hierarchy level n, the name of its relative top-cell (n+1) and the coordinates of the respective BlackBox inside its relative top-cell (n+1), can be found below:

Total BlackBox count is summarized below:
{"AN350BB_EdgeCoupler_Lensed_C"=>60}

DSC Rule_05(1) - PASSED (BBR Check(1))

DSC Rule_05(2) - PASSED (BBR Check(2))

DSC Rule_06 - PASSED (Cell Size Check - MPW Standard)

DSC Rule_07- START (Degenerate Boundary Check)
The coordinates of the concerning shapes/boxes/polygons can be found below:

DSC Rule_07 - PASSED (Degenerate Boundary Check)
